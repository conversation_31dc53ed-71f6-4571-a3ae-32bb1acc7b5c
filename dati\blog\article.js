// 文章页面专用脚本

// DOM 元素
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const navMenu = document.getElementById('navMenu');
const articleTOC = document.getElementById('articleTOC');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeArticlePage();
});

// 初始化文章页面
function initializeArticlePage() {
    // 移动端菜单切换
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 生成文章目录
    generateTableOfContents();
    
    // 添加阅读进度条
    addReadingProgress();
    
    // 添加滚动监听
    addScrollListener();
    
    // 初始化分享功能
    initializeSharing();
    
    // 添加代码复制功能
    addCodeCopyButtons();
    
    // 添加图片点击放大
    addImageZoom();
    
    // 添加平滑滚动
    addSmoothScrolling();
}

// 切换移动端菜单
function toggleMobileMenu() {
    navMenu.classList.toggle('active');
    
    // 切换汉堡菜单图标动画
    const spans = mobileMenuBtn.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) span.style.opacity = '0';
            if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            span.style.transform = '';
            span.style.opacity = '';
        }
    });
}

// 生成文章目录
function generateTableOfContents() {
    if (!articleTOC) return;
    
    const headings = document.querySelectorAll('.article-body h2, .article-body h3, .article-body h4');
    if (headings.length === 0) {
        articleTOC.innerHTML = '<p>本文暂无目录</p>';
        return;
    }
    
    let tocHTML = '<ul>';
    
    headings.forEach((heading, index) => {
        const id = `heading-${index}`;
        heading.id = id;
        
        const level = heading.tagName.toLowerCase();
        const text = heading.textContent;
        const className = `toc-${level}`;
        
        tocHTML += `<li><a href="#${id}" class="${className}">${text}</a></li>`;
    });
    
    tocHTML += '</ul>';
    articleTOC.innerHTML = tocHTML;
    
    // 添加目录点击事件
    const tocLinks = articleTOC.querySelectorAll('a');
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 更新活动状态
                updateActiveTOCLink(this);
            }
        });
    });
}

// 更新活动的目录链接
function updateActiveTOCLink(activeLink) {
    const tocLinks = articleTOC.querySelectorAll('a');
    tocLinks.forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

// 添加阅读进度条
function addReadingProgress() {
    const progressContainer = document.createElement('div');
    progressContainer.className = 'reading-progress';
    progressContainer.innerHTML = '<div class="reading-progress-bar"></div>';
    
    document.body.appendChild(progressContainer);
    
    const progressBar = progressContainer.querySelector('.reading-progress-bar');
    
    window.addEventListener('scroll', function() {
        const articleBody = document.querySelector('.article-body');
        if (!articleBody) return;
        
        const articleTop = articleBody.offsetTop;
        const articleHeight = articleBody.offsetHeight;
        const windowHeight = window.innerHeight;
        const scrollTop = window.pageYOffset;
        
        const progress = Math.min(
            Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
            1
        );
        
        progressBar.style.width = `${progress * 100}%`;
    });
}

// 添加滚动监听
function addScrollListener() {
    window.addEventListener('scroll', throttle(function() {
        updateTOCActiveState();
    }, 100));
}

// 更新目录活动状态
function updateTOCActiveState() {
    const headings = document.querySelectorAll('.article-body h2, .article-body h3, .article-body h4');
    const tocLinks = articleTOC.querySelectorAll('a');
    
    let currentHeading = null;
    
    headings.forEach(heading => {
        const rect = heading.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 0) {
            currentHeading = heading;
        }
    });
    
    if (currentHeading) {
        const currentId = currentHeading.id;
        const currentLink = articleTOC.querySelector(`a[href="#${currentId}"]`);
        
        if (currentLink) {
            tocLinks.forEach(link => {
                link.classList.remove('active');
            });
            currentLink.classList.add('active');
        }
    }
}

// 初始化分享功能
function initializeSharing() {
    // 分享按钮事件已在HTML中定义，这里可以添加额外的分享逻辑
}

// 分享到社交媒体
function shareToSocial(platform) {
    const title = document.querySelector('.article-title').textContent;
    const url = window.location.href;
    const text = `我正在阅读：${title}`;
    
    let shareUrl = '';
    
    switch (platform) {
        case 'wechat':
            // 微信分享通常需要特殊处理，这里只是示例
            showToast('请复制链接手动分享到微信', 'info');
            copyArticleLink();
            break;
        case 'weibo':
            shareUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(text)}`;
            break;
        case 'qq':
            shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(url)}&title=${encodeURIComponent(text)}`;
            break;
        default:
            return;
    }
    
    if (shareUrl) {
        window.open(shareUrl, '_blank', 'width=600,height=400');
    }
}

// 复制文章链接
function copyArticleLink() {
    const url = window.location.href;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            showToast('链接已复制到剪贴板', 'success');
        }).catch(() => {
            fallbackCopyText(url);
        });
    } else {
        fallbackCopyText(url);
    }
}

// 备用复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
        showToast('链接已复制到剪贴板', 'success');
    } catch (err) {
        showToast('复制失败，请手动复制链接', 'error');
    }
    document.body.removeChild(textArea);
}

// 添加代码复制功能
function addCodeCopyButtons() {
    const codeBlocks = document.querySelectorAll('.article-body pre');
    
    codeBlocks.forEach(block => {
        const copyButton = document.createElement('button');
        copyButton.className = 'code-copy-btn';
        copyButton.textContent = '复制';
        copyButton.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        `;
        
        block.style.position = 'relative';
        block.appendChild(copyButton);
        
        block.addEventListener('mouseenter', function() {
            copyButton.style.opacity = '1';
        });
        
        block.addEventListener('mouseleave', function() {
            copyButton.style.opacity = '0';
        });
        
        copyButton.addEventListener('click', function(e) {
            e.stopPropagation();
            const code = block.querySelector('code');
            const text = code ? code.textContent : block.textContent;
            
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    copyButton.textContent = '已复制';
                    setTimeout(() => {
                        copyButton.textContent = '复制';
                    }, 2000);
                });
            }
        });
    });
}

// 添加图片点击放大
function addImageZoom() {
    const images = document.querySelectorAll('.article-body img');
    
    images.forEach(img => {
        img.style.cursor = 'pointer';
        img.addEventListener('click', function() {
            showImageModal(this.src, this.alt);
        });
    });
}

// 显示图片模态框
function showImageModal(src, alt) {
    const modal = document.createElement('div');
    modal.className = 'image-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;
    
    const img = document.createElement('img');
    img.src = src;
    img.alt = alt;
    img.style.cssText = `
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    `;
    
    const closeBtn = document.createElement('button');
    closeBtn.textContent = '×';
    closeBtn.style.cssText = `
        position: absolute;
        top: 20px;
        right: 20px;
        background: rgba(255, 255, 255, 0.2);
        color: white;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        font-size: 24px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
    `;
    
    modal.appendChild(img);
    modal.appendChild(closeBtn);
    document.body.appendChild(modal);
    
    // 显示动画
    setTimeout(() => {
        modal.style.opacity = '1';
    }, 10);
    
    // 关闭事件
    const closeModal = () => {
        modal.style.opacity = '0';
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    };
    
    closeBtn.addEventListener('click', closeModal);
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // ESC键关闭
    const handleKeydown = (e) => {
        if (e.key === 'Escape') {
            closeModal();
            document.removeEventListener('keydown', handleKeydown);
        }
    };
    document.addEventListener('keydown', handleKeydown);
}

// 添加平滑滚动
function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    const colors = {
        success: 'var(--success-color)',
        error: 'var(--danger-color)',
        info: 'var(--primary-color)',
        warning: 'var(--warning-color)'
    };
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: ${colors[type] || colors.info};
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 添加键盘快捷键
document.addEventListener('keydown', function(e) {
    // Ctrl+K 或 Cmd+K 聚焦搜索框（如果有的话）
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        // 可以添加搜索功能
    }
    
    // 左右箭头键导航
    if (e.key === 'ArrowLeft') {
        const prevLink = document.querySelector('.prev-article');
        if (prevLink) {
            window.location.href = prevLink.href;
        }
    }
    
    if (e.key === 'ArrowRight') {
        const nextLink = document.querySelector('.next-article');
        if (nextLink) {
            window.location.href = nextLink.href;
        }
    }
});

// 添加打印样式优化
function optimizePrintStyles() {
    const printStyles = `
        @media print {
            .article-sidebar,
            .article-footer,
            .reading-progress,
            .code-copy-btn {
                display: none !important;
            }
            
            .article-layout {
                grid-template-columns: 1fr !important;
            }
            
            .article-content {
                box-shadow: none !important;
                border-radius: 0 !important;
            }
            
            .article-body {
                font-size: 12pt !important;
                line-height: 1.5 !important;
            }
            
            .article-body h2 {
                page-break-after: avoid;
            }
            
            .highlight-box,
            .tip-box,
            .info-box,
            .warning-box {
                page-break-inside: avoid;
            }
        }
    `;
    
    const styleSheet = document.createElement('style');
    styleSheet.textContent = printStyles;
    document.head.appendChild(styleSheet);
}

// 初始化打印优化
optimizePrintStyles();

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时的处理
        console.log('文章页面重新可见');
    }
});

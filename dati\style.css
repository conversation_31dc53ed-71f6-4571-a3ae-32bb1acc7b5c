/* CSS 变量定义 */
:root {
    /* 主色调 */
    --primary-color: #6366f1;
    --primary-hover: #5855eb;
    --primary-light: #a5b4fc;
    --primary-dark: #4338ca;
    
    /* 辅助色 */
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;
    --purple-500: #8b5cf6;
    --orange-500: #f97316;
    
    /* 中性色 */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* 字体 */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    
    /* 间距 */
    --spacing-1: 0.25rem;
    --spacing-2: 0.5rem;
    --spacing-3: 0.75rem;
    --spacing-4: 1rem;
    --spacing-5: 1.25rem;
    --spacing-6: 1.5rem;
    --spacing-8: 2rem;
    --spacing-10: 2.5rem;
    --spacing-12: 3rem;
    --spacing-16: 4rem;
    --spacing-20: 5rem;
    
    /* 圆角 */
    --border-radius-sm: 0.25rem;
    --border-radius: 0.375rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-2xl: 1.5rem;
    --border-radius-full: 9999px;
    
    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* 过渡 */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 300ms ease-in-out;
    --transition-slow: 500ms ease-in-out;
    
    /* 断点 */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* 基础重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
    .container {
        padding: 0 var(--spacing-6);
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 var(--spacing-8);
    }
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-3) var(--spacing-6);
    font-size: var(--font-size-base);
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    box-shadow: var(--shadow-md);
    transform: translateY(-1px);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
    box-shadow: var(--shadow);
}

/* 导航栏 */
.header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-200);
    position: sticky;
    top: 0;
    z-index: 50;
    backdrop-filter: blur(10px);
}

.nav {
    padding: var(--spacing-4) 0;
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1400px;
    margin: 0 auto;
    height: 72px;
}

.nav-brand .logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    text-decoration: none;
    color: var(--gray-900);
    font-weight: 700;
    font-size: var(--font-size-xl);
    transition: var(--transition-fast);
}

.nav-brand .logo:hover {
    color: var(--primary-color);
}

.logo-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.logo-text {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.mobile-menu-btn {
    display: none;
    flex-direction: column;
    gap: var(--spacing-1);
    background: none;
    border: none;
    cursor: pointer;
    padding: var(--spacing-2);
}

.mobile-menu-btn span {
    width: 24px;
    height: 2px;
    background-color: var(--gray-600);
    transition: var(--transition-fast);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-8);
}

.nav-links {
    display: flex;
    align-items: center;
    gap: var(--spacing-6);
    list-style: none;
}

.nav-link {
    text-decoration: none;
    color: var(--gray-600);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius-full);
}

/* 下拉菜单 */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-3);
    min-width: 280px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    list-style: none;
    z-index: 10;
}

.dropdown:hover .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    text-decoration: none;
    color: var(--gray-700);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-fast);
}

.dropdown-link:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.dropdown-emoji {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.dropdown-content {
    flex: 1;
}

.dropdown-title {
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: 2px;
}

.dropdown-desc {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

.dropdown-icon {
    transition: var(--transition-fast);
}

.dropdown:hover .dropdown-icon {
    transform: rotate(180deg);
}

/* 搜索框 */
.search-box {
    display: flex;
    align-items: center;
    background-color: var(--gray-50);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-full);
    padding: var(--spacing-2);
    transition: var(--transition-fast);
}

.search-box:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-input {
    background: none;
    border: none;
    outline: none;
    padding: var(--spacing-1) var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    width: 200px;
}

.search-input::placeholder {
    color: var(--gray-400);
}

.search-btn {
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: var(--spacing-1);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.search-btn:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

/* 右侧功能区 */
.nav-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.notification-btn {
    position: relative;
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.notification-btn:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--danger-color);
    color: var(--white);
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--border-radius-full);
    min-width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-btn {
    background: none;
    border: none;
    color: var(--gray-600);
    cursor: pointer;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.user-btn:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

/* 英雄区域 */
.hero {
    padding: var(--spacing-20) 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

.hero .container {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-12);
    align-items: center;
}

@media (min-width: 1024px) {
    .hero .container {
        grid-template-columns: 1fr 1fr;
    }
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
    line-height: 1.2;
}

@media (min-width: 768px) {
    .hero-title {
        font-size: var(--font-size-5xl);
    }
}

.hero-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    margin-bottom: var(--spacing-8);
    line-height: 1.6;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-4);
    flex-wrap: wrap;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: var(--border-radius-2xl);
    box-shadow: var(--shadow-xl);
}

/* 章节标题 */
.section-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    text-align: center;
    margin-bottom: var(--spacing-12);
}

/* 分类板块 */
.categories {
    padding: var(--spacing-20) 0;
}

.categories-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 640px) {
    .categories-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .categories-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.category-card {
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    text-align: center;
    transition: all var(--transition-normal);
    cursor: pointer;
    box-shadow: var(--shadow-sm);
}

.category-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.category-icon {
    margin-bottom: var(--spacing-6);
}

.category-icon img {
    width: 80px;
    height: 80px;
    border-radius: var(--border-radius-xl);
}

.category-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.category-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    line-height: 1.5;
}

.category-stats {
    display: flex;
    justify-content: center;
}

.quiz-count {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* 热门测验 */
.featured-quizzes {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.quizzes-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .quizzes-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .quizzes-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.quiz-card {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: all var(--transition-normal);
}

.quiz-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.quiz-image {
    position: relative;
    overflow: hidden;
}

.quiz-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-normal);
}

.quiz-card:hover .quiz-image img {
    transform: scale(1.05);
}

.quiz-category {
    position: absolute;
    top: var(--spacing-4);
    left: var(--spacing-4);
    background-color: var(--white);
    color: var(--primary-color);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.quiz-content {
    padding: var(--spacing-6);
}

.quiz-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.quiz-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    line-height: 1.5;
}

.quiz-meta {
    display: flex;
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    flex-wrap: wrap;
}

.quiz-meta span {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    background-color: var(--gray-100);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
}

.quiz-btn {
    width: 100%;
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-3) var(--spacing-6);
    border-radius: var(--border-radius-lg);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quiz-btn:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

/* 统计数据 */
.stats {
    padding: var(--spacing-20) 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-2);
}

.stat-label {
    color: var(--gray-600);
    font-weight: 500;
}

/* 页脚 */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: var(--spacing-16) 0 var(--spacing-8);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-12);
}

@media (min-width: 768px) {
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr 1fr;
    }
}

.footer-brand .footer-logo {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    text-decoration: none;
    color: var(--white);
    font-weight: 600;
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.footer-description {
    line-height: 1.6;
    max-width: 300px;
}

.footer-title {
    color: var(--white);
    font-weight: 600;
    margin-bottom: var(--spacing-4);
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: var(--spacing-2);
}

.footer-links a {
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-links a:hover {
    color: var(--white);
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: var(--spacing-8);
    border-top: 1px solid var(--gray-700);
    flex-wrap: wrap;
    gap: var(--spacing-4);
}

.footer-social {
    display: flex;
    gap: var(--spacing-4);
}

.social-link {
    color: var(--gray-400);
    transition: var(--transition-fast);
}

.social-link:hover {
    color: var(--white);
}

/* 响应式设计 */
@media (max-width: 1023px) {
    .mobile-menu-btn {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--white);
        border-top: 1px solid var(--gray-200);
        padding: var(--spacing-6);
        flex-direction: column;
        gap: var(--spacing-6);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all var(--transition-normal);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-links {
        flex-direction: column;
        width: 100%;
        gap: var(--spacing-4);
    }
    
    .search-box {
        width: 100%;
    }
    
    .search-input {
        width: 100%;
    }
    
    .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        padding: 0;
        margin-top: var(--spacing-2);
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.6s ease-out;
}

/* 工具类 */
.text-center {
    text-align: center;
}

.hidden {
    display: none;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* 新首页布局样式 */

/* 主要布局 */
.main {
    padding: var(--spacing-6) 0;
    background-color: var(--gray-50);
}

.main-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    max-width: 1400px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .main-layout {
        grid-template-columns: 2fr 1fr;
    }
}

.main-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-8);
}

/* 特色测验卡片 */
.featured-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
    margin-bottom: var(--spacing-8);
}

@media (min-width: 768px) {
    .featured-cards {
        grid-template-columns: 2fr 1fr;
    }
}

.featured-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    cursor: pointer;
    transition: var(--transition-fast);
}

.featured-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.featured-card.large {
    position: relative;
}

.featured-cards-small {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.featured-card.small {
    flex: 1;
}

.card-image {
    position: relative;
    overflow: hidden;
}

.card-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: var(--transition-fast);
}

.featured-card.small .card-image img {
    height: 120px;
}

.featured-card:hover .card-image img {
    transform: scale(1.05);
}

.card-badge {
    position: absolute;
    top: var(--spacing-3);
    left: var(--spacing-3);
    background: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.card-badge.personality {
    background: var(--warning-color);
}

.card-content {
    padding: var(--spacing-4);
}

.card-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    line-height: 1.3;
}

.featured-card.small .card-title {
    font-size: var(--font-size-base);
}

.card-stats {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

/* 分类测验部分 */
.category-section {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.category-tabs {
    display: flex;
    background: var(--gray-50);
    border-bottom: 1px solid var(--gray-200);
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.category-tabs::-webkit-scrollbar {
    display: none;
}

.category-tab {
    flex: 0 0 auto;
    padding: var(--spacing-4) var(--spacing-6);
    background: none;
    border: none;
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
    min-width: 120px;
}

.category-tab.active {
    background: var(--white);
    color: var(--primary-color);
    border-bottom: 2px solid var(--primary-color);
}

.category-content {
    padding: var(--spacing-6);
}

.category-cards {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

@media (min-width: 640px) {
    .category-cards {
        grid-template-columns: 1fr 1fr;
    }
}

.category-card {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
}

.category-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.category-card .card-image {
    flex-shrink: 0;
}

.category-card .card-image img {
    width: 80px;
    height: 60px;
    border-radius: var(--border-radius);
}

.category-card .card-content {
    flex: 1;
    padding: 0;
}

.category-card .card-title {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-1);
}

.card-description {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    margin-bottom: var(--spacing-2);
}

.participants {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 侧边栏 */
.sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.sidebar-widget {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-lg);
}

.widget-header {
    margin-bottom: var(--spacing-4);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.widget-tabs {
    display: flex;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
}

.widget-tab {
    padding: var(--spacing-1) var(--spacing-3);
    background: var(--gray-100);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    cursor: pointer;
    transition: var(--transition-fast);
}

.widget-tab.active {
    background: var(--primary-color);
    color: var(--white);
}

/* 排行榜 */
.leaderboard {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-4);
}

.leaderboard-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-radius: var(--border-radius-lg);
    transition: var(--transition-fast);
}

.leaderboard-item:hover {
    background: var(--gray-50);
}

.rank {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gray-100);
    border-radius: 50%;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-700);
}

.leaderboard-item:nth-child(1) .rank {
    background: var(--warning-color);
    color: var(--white);
}

.leaderboard-item:nth-child(2) .rank {
    background: var(--gray-400);
    color: var(--white);
}

.leaderboard-item:nth-child(3) .rank {
    background: var(--orange-500);
    color: var(--white);
}

.player-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    flex: 1;
}

.player-avatar img {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.player-details {
    flex: 1;
}

.player-name {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: 2px;
}

.player-score {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

.player-badge {
    font-size: var(--font-size-lg);
}

.view-all-btn {
    width: 100%;
    padding: var(--spacing-2);
    background: var(--primary-light);
    color: var(--primary-color);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.view-all-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* 连胜记录 */
.streak-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.streak-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-2);
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.streak-item:hover {
    background: var(--gray-50);
}

.streak-item .player-info {
    flex: 1;
}

.streak-item .player-avatar img {
    width: 24px;
    height: 24px;
}

.streak-item .player-name {
    font-size: var(--font-size-xs);
}

.streak-count {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--orange-500);
}

/* 最新测验网格 */
.latest-quizzes {
    padding: var(--spacing-8) 0;
    background: var(--white);
}

.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-6);
}

.section-link {
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-lg);
    font-weight: 500;
    transition: var(--transition-fast);
}

.section-link:hover {
    color: var(--primary-hover);
}

.quiz-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
}

.quiz-card {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    cursor: pointer;
    transition: var(--transition-fast);
}

.quiz-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quiz-image {
    position: relative;
    overflow: hidden;
}

.quiz-image img {
    width: 100%;
    height: 160px;
    object-fit: cover;
    transition: var(--transition-fast);
}

.quiz-card:hover .quiz-image img {
    transform: scale(1.05);
}

.quiz-category {
    position: absolute;
    top: var(--spacing-2);
    left: var(--spacing-2);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--border-radius);
    font-size: var(--font-size-xs);
    font-weight: 500;
    text-transform: uppercase;
    color: var(--white);
}

.quiz-category.food {
    background: var(--warning-color);
}

.quiz-category.lifestyle {
    background: var(--purple-500);
}

.quiz-category.personality {
    background: var(--danger-color);
}

.quiz-category.language {
    background: var(--success-color);
}

.quiz-category.geography {
    background: var(--orange-500);
}

.quiz-content {
    padding: var(--spacing-4);
}

.quiz-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
    line-height: 1.4;
}

.quiz-description {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-3);
    line-height: 1.4;
}

.quiz-stats {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.quiz-stats .participants {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 响应式设计 */
@media (max-width: 1023px) {
    .main-layout {
        grid-template-columns: 1fr;
    }

    .sidebar {
        order: -1;
    }
}

@media (max-width: 767px) {
    .featured-cards {
        grid-template-columns: 1fr;
    }

    .featured-cards-small {
        flex-direction: row;
    }

    .category-cards {
        grid-template-columns: 1fr;
    }

    .quiz-grid {
        grid-template-columns: 1fr;
    }

    .widget-tabs {
        flex-direction: column;
    }

    .widget-tab {
        text-align: center;
    }
}

@media (max-width: 480px) {
    .main {
        padding: var(--spacing-4) 0;
    }

    .main-layout {
        gap: var(--spacing-4);
    }

    .featured-cards-small {
        flex-direction: column;
    }

    .sidebar-widget {
        padding: var(--spacing-4);
    }

    .leaderboard-item {
        padding: var(--spacing-2);
    }

    .player-avatar img {
        width: 28px;
        height: 28px;
    }
}

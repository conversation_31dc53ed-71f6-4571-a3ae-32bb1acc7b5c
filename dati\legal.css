/* 法律页面通用样式 */

/* 法律页面主体 */
.legal-main {
    padding: var(--spacing-8) 0;
    min-height: calc(100vh - 160px);
    background-color: var(--gray-50);
}

/* 法律页面头部 */
.legal-header {
    text-align: center;
    margin-bottom: var(--spacing-12);
    padding: var(--spacing-8) 0;
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow);
}

.legal-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.legal-subtitle {
    color: var(--gray-600);
    font-size: var(--font-size-base);
}

/* 法律内容容器 */
.legal-content {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .legal-content {
        grid-template-columns: 300px 1fr;
    }
}

/* 目录 */
.legal-toc {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    height: fit-content;
    position: sticky;
    top: 100px;
}

.legal-toc h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--primary-color);
}

.legal-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.legal-toc li {
    margin-bottom: var(--spacing-2);
}

.legal-toc a {
    display: block;
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.legal-toc a:hover,
.legal-toc a.active {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

/* 法律章节 */
.legal-sections {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow);
}

.legal-section {
    margin-bottom: var(--spacing-12);
    padding-bottom: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.legal-section:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.legal-section h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
    padding-bottom: var(--spacing-3);
    border-bottom: 2px solid var(--primary-color);
}

.legal-section h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin: var(--spacing-6) 0 var(--spacing-4);
}

.legal-section h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-700);
    margin: var(--spacing-4) 0 var(--spacing-3);
}

.legal-section p {
    color: var(--gray-700);
    line-height: 1.8;
    margin-bottom: var(--spacing-4);
    text-align: justify;
}

.legal-section ul,
.legal-section ol {
    margin: var(--spacing-4) 0;
    padding-left: var(--spacing-6);
}

.legal-section li {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-2);
}

.legal-section strong {
    color: var(--gray-900);
    font-weight: 600;
}

/* 联系信息样式 */
.contact-info {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    margin: var(--spacing-4) 0;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-3);
    padding: var(--spacing-2) 0;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item strong {
    min-width: 80px;
    color: var(--gray-900);
    font-weight: 600;
}

/* 高亮框 */
.highlight-box {
    background-color: var(--primary-light);
    border-left: 4px solid var(--primary-color);
    padding: var(--spacing-4);
    margin: var(--spacing-4) 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.highlight-box p {
    margin-bottom: 0;
    color: var(--primary-dark);
}

/* 警告框 */
.warning-box {
    background-color: #fef3cd;
    border-left: 4px solid var(--warning-color);
    padding: var(--spacing-4);
    margin: var(--spacing-4) 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.warning-box p {
    margin-bottom: 0;
    color: #856404;
}

/* 信息框 */
.info-box {
    background-color: #d1ecf1;
    border-left: 4px solid var(--info-color);
    padding: var(--spacing-4);
    margin: var(--spacing-4) 0;
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.info-box p {
    margin-bottom: 0;
    color: #0c5460;
}

/* 表格样式 */
.legal-table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-4) 0;
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.legal-table th,
.legal-table td {
    padding: var(--spacing-3) var(--spacing-4);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.legal-table th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.legal-table td {
    color: var(--gray-700);
}

.legal-table tr:last-child td {
    border-bottom: none;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-fast);
    opacity: 0;
    visibility: hidden;
    z-index: 100;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

.back-to-top svg {
    width: 20px;
    height: 20px;
}

/* 响应式设计 */
@media (max-width: 1023px) {
    .legal-toc {
        position: static;
        order: 2;
    }
    
    .legal-sections {
        order: 1;
    }
}

@media (max-width: 768px) {
    .legal-main {
        padding: var(--spacing-4) 0;
    }
    
    .legal-header {
        margin-bottom: var(--spacing-8);
        padding: var(--spacing-6) var(--spacing-4);
    }
    
    .legal-title {
        font-size: var(--font-size-2xl);
    }
    
    .legal-content {
        gap: var(--spacing-6);
    }
    
    .legal-toc,
    .legal-sections {
        padding: var(--spacing-6);
    }
    
    .legal-section {
        margin-bottom: var(--spacing-8);
    }
    
    .legal-section h2 {
        font-size: var(--font-size-xl);
    }
    
    .legal-section h3 {
        font-size: var(--font-size-lg);
    }
    
    .legal-section h4 {
        font-size: var(--font-size-base);
    }
    
    .contact-item {
        flex-direction: column;
        gap: var(--spacing-1);
    }
    
    .contact-item strong {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .legal-header {
        padding: var(--spacing-4);
    }
    
    .legal-title {
        font-size: var(--font-size-xl);
    }
    
    .legal-toc,
    .legal-sections {
        padding: var(--spacing-4);
    }
    
    .legal-section ul,
    .legal-section ol {
        padding-left: var(--spacing-4);
    }
    
    .back-to-top {
        bottom: var(--spacing-4);
        right: var(--spacing-4);
        width: 45px;
        height: 45px;
    }
}

/* 打印样式 */
@media print {
    .legal-toc,
    .back-to-top,
    .header,
    .footer {
        display: none;
    }
    
    .legal-main {
        padding: 0;
        background: white;
    }
    
    .legal-content {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .legal-sections {
        box-shadow: none;
        border-radius: 0;
        padding: 0;
    }
    
    .legal-section {
        page-break-inside: avoid;
    }
    
    .legal-section h2 {
        page-break-after: avoid;
    }
}

/* 滚动条样式 */
.legal-sections::-webkit-scrollbar {
    width: 8px;
}

.legal-sections::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--border-radius);
}

.legal-sections::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: var(--border-radius);
}

.legal-sections::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* 选择文本样式 */
.legal-sections ::selection {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

// 结果页面专用脚本

// 结果数据
let quizResult = null;

// DOM 元素
const resultIcon = document.getElementById('resultIcon');
const resultTitle = document.getElementById('resultTitle');
const resultSubtitle = document.getElementById('resultSubtitle');
const scoreNumber = document.getElementById('scoreNumber');
const scoreTotal = document.getElementById('scoreTotal');
const scorePercentage = document.getElementById('scorePercentage');
const scoreDescription = document.getElementById('scoreDescription');
const scoreCircle = document.getElementById('scoreCircle');
const analysisContent = document.getElementById('analysisContent');
const personalitySection = document.getElementById('personalitySection');
const personalityResult = document.getElementById('personalityResult');
const timeSpent = document.getElementById('timeSpent');
const accuracy = document.getElementById('accuracy');
const category = document.getElementById('category');
const completedTime = document.getElementById('completedTime');
const reviewContent = document.getElementById('reviewContent');
const shareModal = document.getElementById('shareModal');
const sharePreview = document.getElementById('sharePreview');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeResult();
});

// 初始化结果页面
function initializeResult() {
    try {
        // 从 localStorage 获取测验结果
        const resultData = localStorage.getItem('quizResult');
        
        if (!resultData) {
            throw new Error('没有找到测验结果');
        }
        
        quizResult = JSON.parse(resultData);
        
        // 显示结果
        displayResult();
        
    } catch (error) {
        console.error('加载结果失败:', error);
        alert('加载结果失败，请重新进行测验。');
        window.location.href = 'index.html';
    }
}

// 显示结果
function displayResult() {
    // 设置基本信息
    setupBasicInfo();
    
    // 显示分数
    displayScore();
    
    // 显示分析
    displayAnalysis();
    
    // 显示统计信息
    displayStats();
    
    // 显示题目回顾
    displayReview();
    
    // 如果是个性测试，显示个性结果
    if (quizResult.quiz.category === '个性测试') {
        displayPersonalityResult();
    }
}

// 设置基本信息
function setupBasicInfo() {
    const percentage = Math.round((quizResult.score / quizResult.totalQuestions) * 100);
    
    // 根据分数设置图标和标题
    if (percentage >= 80) {
        resultIcon.textContent = '🏆';
        resultTitle.textContent = '太棒了！';
        resultSubtitle.textContent = '你的表现非常出色！';
    } else if (percentage >= 60) {
        resultIcon.textContent = '🎉';
        resultTitle.textContent = '做得不错！';
        resultSubtitle.textContent = '你的知识水平很好！';
    } else if (percentage >= 40) {
        resultIcon.textContent = '👍';
        resultTitle.textContent = '继续努力！';
        resultSubtitle.textContent = '还有提升的空间！';
    } else {
        resultIcon.textContent = '💪';
        resultTitle.textContent = '加油！';
        resultSubtitle.textContent = '多练习会更好！';
    }
}

// 显示分数
function displayScore() {
    const percentage = Math.round((quizResult.score / quizResult.totalQuestions) * 100);
    
    // 动画显示分数
    animateScore(quizResult.score);
    
    scoreTotal.textContent = `/ ${quizResult.totalQuestions}`;
    scorePercentage.textContent = `${percentage}%`;
    
    // 设置分数描述
    let description = '';
    if (quizResult.quiz.category === '个性测试') {
        description = '个性测试没有对错之分，每个答案都反映了你独特的性格特征。';
    } else {
        if (percentage >= 80) {
            description = '优秀！你在这个领域的知识非常扎实。';
        } else if (percentage >= 60) {
            description = '良好！你掌握了大部分知识点。';
        } else if (percentage >= 40) {
            description = '及格！继续学习会有更大进步。';
        } else {
            description = '需要加强！建议多学习相关知识。';
        }
    }
    
    scoreDescription.textContent = description;
    
    // 设置圆形进度条
    scoreCircle.style.background = `conic-gradient(var(--primary-color) ${percentage * 3.6}deg, var(--gray-200) 0deg)`;
}

// 动画显示分数
function animateScore(targetScore) {
    let currentScore = 0;
    const increment = targetScore / 30; // 30帧动画
    
    const animation = setInterval(() => {
        currentScore += increment;
        if (currentScore >= targetScore) {
            currentScore = targetScore;
            clearInterval(animation);
        }
        scoreNumber.textContent = Math.floor(currentScore);
    }, 50);
}

// 显示分析
function displayAnalysis() {
    const percentage = Math.round((quizResult.score / quizResult.totalQuestions) * 100);
    let analysisText = '';
    
    if (quizResult.quiz.category === '个性测试') {
        analysisText = `
            <p>通过这次个性测试，我们分析了你在不同情境下的行为倾向和思维模式。</p>
            <p>你的回答显示了独特的个性特征，这些特征在日常生活和工作中都会有所体现。</p>
            <p>了解自己的性格类型有助于更好地认识自己，改善人际关系，并在职业发展中做出更明智的选择。</p>
        `;
    } else {
        analysisText = `
            <p>在这次${quizResult.quiz.category}测验中，你答对了 ${quizResult.score} 道题，正确率为 ${percentage}%。</p>
        `;
        
        if (percentage >= 80) {
            analysisText += `
                <p>你在${quizResult.quiz.category}方面表现出色，知识储备丰富，理解深入。建议继续保持学习热情，探索更深层次的知识。</p>
            `;
        } else if (percentage >= 60) {
            analysisText += `
                <p>你在${quizResult.quiz.category}方面有良好的基础，但还有提升空间。建议针对错误的题目进行复习，加强薄弱环节。</p>
            `;
        } else {
            analysisText += `
                <p>你在${quizResult.quiz.category}方面需要加强学习。建议系统性地学习相关知识，多做练习来提高水平。</p>
            `;
        }
        
        // 添加具体建议
        const incorrectCount = quizResult.totalQuestions - quizResult.score;
        if (incorrectCount > 0) {
            analysisText += `
                <p>你有 ${incorrectCount} 道题答错了，可以查看下方的题目回顾来了解正确答案和解释。</p>
            `;
        }
    }
    
    analysisContent.innerHTML = analysisText;
}

// 显示个性结果
function displayPersonalityResult() {
    personalitySection.classList.remove('hidden');
    
    // 分析个性类型
    const personalityTypes = {};
    
    quizResult.userAnswers.forEach((answerIndex, questionIndex) => {
        if (answerIndex !== null) {
            const question = quizResult.quiz.questions[questionIndex];
            if (question.personality && question.personality[answerIndex]) {
                const type = question.personality[answerIndex];
                personalityTypes[type] = (personalityTypes[type] || 0) + 1;
            }
        }
    });
    
    // 找出主导性格类型
    let dominantType = '平衡型';
    let maxCount = 0;
    
    for (const [type, count] of Object.entries(personalityTypes)) {
        if (count > maxCount) {
            maxCount = count;
            dominantType = type;
        }
    }
    
    // 生成个性结果HTML
    const personalityDescriptions = {
        '外向型': '你喜欢与人交往，从社交互动中获得能量，善于表达自己的想法。',
        '内向型': '你更喜欢独处或小群体活动，从内心世界获得能量，深思熟虑。',
        '直觉型': '你相信第一感觉，善于快速做决定，具有敏锐的洞察力。',
        '理性型': '你喜欢分析和逻辑思考，做决定前会仔细权衡各种因素。',
        '社交型': '你重视人际关系，善于与他人合作，具有很强的同理心。',
        '独立型': '你喜欢独立工作，有很强的自主性，能够承担责任。',
        '平衡型': '你在各个方面都比较均衡，能够根据情况灵活调整自己的行为方式。'
    };
    
    const resultHTML = `
        <div class="personality-type">${dominantType}</div>
        <div class="personality-description">
            ${personalityDescriptions[dominantType] || '你具有独特的个性特征，在不同情况下会表现出不同的行为模式。'}
        </div>
        <div class="personality-traits">
            ${Object.entries(personalityTypes).map(([type, count]) => `
                <div class="trait-item">
                    <div class="trait-name">${type}</div>
                    <div class="trait-score">${count}/${quizResult.totalQuestions}</div>
                </div>
            `).join('')}
        </div>
    `;
    
    personalityResult.innerHTML = resultHTML;
}

// 显示统计信息
function displayStats() {
    // 用时
    const timeSpentMs = quizResult.timeSpent;
    const timeSpentText = formatTime(timeSpentMs);
    timeSpent.textContent = timeSpentText;
    
    // 正确率
    const percentage = Math.round((quizResult.score / quizResult.totalQuestions) * 100);
    accuracy.textContent = `${percentage}%`;
    
    // 分类
    category.textContent = quizResult.quiz.category;
    
    // 完成时间
    const completedDate = new Date(quizResult.completedAt);
    completedTime.textContent = formatDate(completedDate);
}

// 显示题目回顾
function displayReview() {
    const reviewHTML = quizResult.quiz.questions.map((question, index) => {
        const userAnswer = quizResult.userAnswers[index];
        const isCorrect = question.correct === -1 || userAnswer === question.correct; // 个性测试都算对
        
        let reviewClass = '';
        let statusText = '';
        
        if (question.correct === -1) {
            reviewClass = 'correct';
            statusText = '已回答';
        } else if (isCorrect) {
            reviewClass = 'correct';
            statusText = '正确';
        } else {
            reviewClass = 'incorrect';
            statusText = '错误';
        }
        
        let answersHTML = '';
        if (question.correct !== -1) {
            answersHTML = `
                <div class="review-answers">
                    <div class="review-answer user-answer">
                        你的答案: ${userAnswer !== null ? question.options[userAnswer] : '未回答'}
                    </div>
                    <div class="review-answer correct-answer">
                        正确答案: ${question.options[question.correct]}
                    </div>
                    ${question.explanation ? `
                        <div class="review-answer">
                            解释: ${question.explanation}
                        </div>
                    ` : ''}
                </div>
            `;
        } else {
            answersHTML = `
                <div class="review-answers">
                    <div class="review-answer user-answer">
                        你的选择: ${userAnswer !== null ? question.options[userAnswer] : '未回答'}
                    </div>
                </div>
            `;
        }
        
        return `
            <div class="review-question ${reviewClass}">
                <div class="review-question-text">
                    第${index + 1}题: ${question.question} (${statusText})
                </div>
                ${answersHTML}
            </div>
        `;
    }).join('');
    
    reviewContent.innerHTML = reviewHTML;
}

// 重新测验
function retakeQuiz() {
    // 清除当前结果
    localStorage.removeItem('quizResult');
    
    // 重新开始同一个测验
    localStorage.setItem('currentQuizType', quizResult.quizType);
    window.location.href = 'quiz.html';
}

// 分享结果
function shareResult() {
    const percentage = Math.round((quizResult.score / quizResult.totalQuestions) * 100);
    
    const previewHTML = `
        <h4>我刚完成了"${quizResult.quiz.title}"测验！</h4>
        <p>得分: ${quizResult.score}/${quizResult.totalQuestions} (${percentage}%)</p>
        <p>分类: ${quizResult.quiz.category}</p>
        <p>来 QuizMaster 挑战你的知识吧！</p>
    `;
    
    sharePreview.innerHTML = previewHTML;
    shareModal.classList.add('active');
}

// 关闭分享模态框
function closeShareModal() {
    shareModal.classList.remove('active');
}

// 分享到社交媒体
function shareToSocial(platform) {
    const percentage = Math.round((quizResult.score / quizResult.totalQuestions) * 100);
    const text = `我刚在QuizMaster完成了"${quizResult.quiz.title}"测验，得分${percentage}%！来挑战你的知识吧！`;
    const url = window.location.origin;
    
    let shareUrl = '';
    
    switch (platform) {
        case 'facebook':
            shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}&quote=${encodeURIComponent(text)}`;
            break;
        case 'twitter':
            shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
            break;
        default:
            return;
    }
    
    window.open(shareUrl, '_blank', 'width=600,height=400');
}

// 复制结果链接
function copyResultLink() {
    const text = `我刚在QuizMaster完成了"${quizResult.quiz.title}"测验，得分${Math.round((quizResult.score / quizResult.totalQuestions) * 100)}%！来挑战你的知识吧！ ${window.location.origin}`;
    
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            alert('链接已复制到剪贴板！');
        }).catch(() => {
            fallbackCopyText(text);
        });
    } else {
        fallbackCopyText(text);
    }
}

// 备用复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
        alert('链接已复制到剪贴板！');
    } catch (err) {
        alert('复制失败，请手动复制链接。');
    }
    document.body.removeChild(textArea);
}

// 格式化时间
function formatTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        return `${remainingSeconds}秒`;
    }
}

// 格式化日期
function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

// 点击模态框外部关闭
shareModal.addEventListener('click', function(e) {
    if (e.target === shareModal) {
        closeShareModal();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && shareModal.classList.contains('active')) {
        closeShareModal();
    }
});

# QuizMaster - 在线测验平台

QuizMaster 是一个现代化的在线测验平台，提供多种类型的测验和个性测试，帮助用户探索知识、了解自己。

## 🌟 功能特点

- **多样化测验**：知识测验、个性测试、技能评估
- **智能分析**：详细的结果分析和个性化建议
- **响应式设计**：完美适配桌面端和移动端
- **现代化界面**：简洁美观的用户界面
- **博客系统**：分享学习技巧和教育资讯
- **用户友好**：直观的操作流程和良好的用户体验

## 📁 项目结构

```
dati/
├── index.html              # 首页
├── quiz.html              # 测验页面
├── result.html            # 结果页面
├── about.html             # 关于我们
├── contact.html           # 联系我们
├── privacy.html           # 隐私政策
├── terms.html             # 服务条款
├── style.css              # 全局样式
├── script.js              # 全局脚本
├── quiz.css               # 测验页面样式
├── quiz.js                # 测验页面脚本
├── result.css             # 结果页面样式
├── result.js              # 结果页面脚本
├── about.css              # 关于页面样式
├── about.js               # 关于页面脚本
├── contact.css            # 联系页面样式
├── contact.js             # 联系页面脚本
├── legal.css              # 法律页面通用样式
├── legal.js               # 法律页面通用脚本
├── blog/                  # 博客目录
│   ├── index.html         # 博客首页
│   ├── blog.css           # 博客样式
│   ├── blog.js            # 博客脚本
│   ├── article.css        # 文章页面样式
│   ├── article.js         # 文章页面脚本
│   └── how-to-improve-memory.html  # 示例文章
└── README.md              # 项目说明
```

## 🚀 快速开始

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd dati
   ```

2. **启动本地服务器**
   ```bash
   # 使用 Python
   python -m http.server 8000
   
   # 或使用 Node.js
   npx serve .
   
   # 或使用 PHP
   php -S localhost:8000
   ```

3. **访问网站**
   打开浏览器访问 `http://localhost:8000`

## 🎯 测验类型

### 知识测验
- **科学常识**：基础科学知识测试
- **历史文化**：历史事件和文化知识
- **地理知识**：世界地理和中国地理
- **文学艺术**：文学作品和艺术常识

### 个性测试
- **性格分析**：MBTI风格的性格测试
- **学习风格**：了解个人学习偏好
- **职业倾向**：发现适合的职业方向
- **情商测试**：评估情绪智力水平

## 💻 技术栈

- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **样式**：CSS Grid, Flexbox, CSS Variables
- **响应式**：Mobile-first 设计
- **动画**：CSS Transitions & Animations
- **字体**：Google Fonts (Inter)
- **图标**：SVG Icons

## 🎨 设计特色

### 色彩系统
- **主色调**：#6366f1 (Indigo)
- **辅助色**：#f59e0b (Amber), #10b981 (Emerald)
- **中性色**：灰度色阶
- **语义色**：成功、警告、错误、信息

### 组件设计
- **卡片式布局**：现代化的卡片设计
- **渐变背景**：优雅的渐变效果
- **阴影系统**：层次分明的阴影
- **圆角设计**：友好的圆角元素

## 📱 响应式设计

- **桌面端**：1024px+
- **平板端**：768px - 1023px
- **手机端**：320px - 767px

## 🔧 自定义配置

### CSS 变量
项目使用 CSS 变量进行主题配置，可在 `style.css` 中修改：

```css
:root {
  --primary-color: #6366f1;
  --primary-hover: #5855eb;
  --primary-light: #e0e7ff;
  --primary-dark: #4338ca;
  /* 更多变量... */
}
```

### 测验数据
测验数据存储在 `script.js` 的 `quizData` 对象中，可以轻松添加新的测验：

```javascript
const quizData = {
  newQuiz: {
    title: "新测验",
    category: "知识测验",
    description: "测验描述",
    questions: [
      // 题目数据
    ]
  }
};
```

## 🌐 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+

## 📝 开发指南

### 添加新测验
1. 在 `script.js` 中的 `quizData` 对象添加测验数据
2. 在首页的测验卡片中添加对应的HTML
3. 确保测验ID与数据键名一致

### 添加新博客文章
1. 在 `blog/` 目录创建新的HTML文件
2. 使用 `article.css` 和 `article.js` 的样式和功能
3. 在博客首页添加文章链接

### 自定义样式
1. 修改CSS变量调整主题色彩
2. 在对应的CSS文件中添加新样式
3. 保持响应式设计原则

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系我们

- **邮箱**：<EMAIL>
- **网站**：https://quizmaster.com
- **电话**：+86 10-8888-9999

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和设计师。

---

**QuizMaster** - 探索知识的边界，挑战自己的极限！

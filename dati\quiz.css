/* 测验页面专用样式 */

/* 测验导航栏 */
.quiz-nav-info {
    display: flex;
    align-items: center;
    gap: var(--spacing-4);
}

.quiz-category {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.quit-btn {
    background-color: var(--danger-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.quit-btn:hover {
    background-color: #dc2626;
    transform: translateY(-1px);
}

/* 测验主体 */
.quiz-main {
    min-height: calc(100vh - 80px);
    padding: var(--spacing-8) 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

/* 测验头部 */
.quiz-header {
    margin-bottom: var(--spacing-8);
}

.quiz-info {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.quiz-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.quiz-description {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 进度条 */
.progress-container {
    max-width: 600px;
    margin: 0 auto;
}

.progress-info {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

.progress-percentage {
    font-weight: 600;
    color: var(--primary-color);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: var(--border-radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: var(--border-radius-full);
    transition: width var(--transition-normal);
    width: 0%;
}

/* 测验内容 */
.quiz-content {
    max-width: 800px;
    margin: 0 auto;
}

/* 题目卡片 */
.question-card {
    background-color: var(--white);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
    transition: all var(--transition-normal);
}

.question-number {
    text-align: center;
    margin-bottom: var(--spacing-6);
}

.question-number span {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.question-text {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    text-align: center;
    margin-bottom: var(--spacing-8);
    line-height: 1.4;
}

/* 选项容器 */
.options-container {
    margin-bottom: var(--spacing-8);
}

.option {
    display: flex;
    align-items: center;
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-3);
    background-color: var(--gray-50);
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.option:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-300);
    transform: translateY(-1px);
}

.option.selected {
    background-color: var(--primary-light);
    border-color: var(--primary-color);
    color: var(--primary-dark);
}

.option.correct {
    background-color: #dcfce7;
    border-color: var(--success-color);
    color: #166534;
}

.option.incorrect {
    background-color: #fef2f2;
    border-color: var(--danger-color);
    color: #991b1b;
}

.option-letter {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background-color: var(--white);
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-full);
    font-weight: 600;
    margin-right: var(--spacing-4);
    transition: var(--transition-fast);
}

.option.selected .option-letter {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--white);
}

.option.correct .option-letter {
    background-color: var(--success-color);
    border-color: var(--success-color);
    color: var(--white);
}

.option.incorrect .option-letter {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
    color: var(--white);
}

.option-text {
    flex: 1;
    font-size: var(--font-size-base);
    line-height: 1.5;
}

/* 题目操作按钮 */
.question-actions {
    display: flex;
    justify-content: space-between;
    gap: var(--spacing-4);
}

.question-actions .btn {
    flex: 1;
    max-width: 200px;
}

@media (max-width: 640px) {
    .question-actions {
        flex-direction: column;
    }
    
    .question-actions .btn {
        max-width: none;
    }
}

/* 危险按钮样式 */
.btn-danger {
    background-color: var(--danger-color);
    color: var(--white);
    border: none;
}

.btn-danger:hover {
    background-color: #dc2626;
}

/* 加载遮罩 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    text-align: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-4);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner p {
    color: var(--gray-600);
    font-weight: 500;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    max-width: 400px;
    width: 90%;
    box-shadow: var(--shadow-xl);
    transform: scale(0.9);
    transition: var(--transition-normal);
}

.modal-overlay.active .modal {
    transform: scale(1);
}

.modal-header {
    margin-bottom: var(--spacing-4);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
}

.modal-body {
    margin-bottom: var(--spacing-6);
}

.modal-body p {
    color: var(--gray-600);
    line-height: 1.5;
}

.modal-actions {
    display: flex;
    gap: var(--spacing-3);
    justify-content: flex-end;
}

/* 题目切换动画 */
.question-card.fade-out {
    opacity: 0;
    transform: translateX(-20px);
}

.question-card.fade-in {
    opacity: 1;
    transform: translateX(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .quiz-main {
        padding: var(--spacing-4) 0;
    }
    
    .quiz-header {
        margin-bottom: var(--spacing-6);
    }
    
    .quiz-title {
        font-size: var(--font-size-2xl);
    }
    
    .quiz-description {
        font-size: var(--font-size-base);
    }
    
    .question-card {
        padding: var(--spacing-6);
    }
    
    .question-text {
        font-size: var(--font-size-lg);
        margin-bottom: var(--spacing-6);
    }
    
    .option {
        padding: var(--spacing-3);
    }
    
    .quiz-nav-info {
        flex-direction: column;
        gap: var(--spacing-2);
    }
}

@media (max-width: 480px) {
    .quiz-category {
        font-size: var(--font-size-xs);
        padding: var(--spacing-1) var(--spacing-2);
    }
    
    .quit-btn {
        font-size: var(--font-size-xs);
        padding: var(--spacing-1) var(--spacing-3);
    }
    
    .question-card {
        padding: var(--spacing-4);
    }
    
    .question-text {
        font-size: var(--font-size-base);
    }
    
    .option-text {
        font-size: var(--font-size-sm);
    }
}

/* 隐藏类 */
.hidden {
    display: none !important;
}

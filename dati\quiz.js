// 测验页面专用脚本

// 测验状态变量
let currentQuiz = null;
let currentQuestionIndex = 0;
let userAnswers = [];
let score = 0;
let quizStartTime = null;
let questionStartTime = null;

// DOM 元素
const loadingOverlay = document.getElementById('loadingOverlay');
const quizCategory = document.getElementById('quizCategory');
const quizTitle = document.getElementById('quizTitle');
const quizDescription = document.getElementById('quizDescription');
const currentQuestionSpan = document.getElementById('currentQuestion');
const totalQuestionsSpan = document.getElementById('totalQuestions');
const progressPercentage = document.getElementById('progressPercentage');
const progressFill = document.getElementById('progressFill');
const questionNumber = document.getElementById('questionNumber');
const questionText = document.getElementById('questionText');
const optionsContainer = document.getElementById('optionsContainer');
const prevBtn = document.getElementById('prevBtn');
const nextBtn = document.getElementById('nextBtn');
const quitModal = document.getElementById('quitModal');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeQuiz();
});

// 初始化测验
async function initializeQuiz() {
    try {
        // 显示加载遮罩
        showLoading();
        
        // 从 localStorage 获取测验类型
        const quizType = localStorage.getItem('currentQuizType');
        
        if (!quizType || !quizData[quizType]) {
            throw new Error('无效的测验类型');
        }
        
        // 设置当前测验
        currentQuiz = quizData[quizType];
        quizStartTime = Date.now();
        
        // 初始化用户答案数组
        userAnswers = new Array(currentQuiz.questions.length).fill(null);
        
        // 模拟加载延迟
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 设置测验信息
        setupQuizInfo();
        
        // 显示第一题
        showQuestion(0);
        
        // 隐藏加载遮罩
        hideLoading();
        
    } catch (error) {
        console.error('初始化测验失败:', error);
        alert('加载测验失败，请返回首页重试。');
        window.location.href = 'index.html';
    }
}

// 设置测验信息
function setupQuizInfo() {
    quizCategory.textContent = currentQuiz.category;
    quizTitle.textContent = currentQuiz.title;
    quizDescription.textContent = currentQuiz.description;
    totalQuestionsSpan.textContent = currentQuiz.questions.length;
}

// 显示题目
function showQuestion(index) {
    if (index < 0 || index >= currentQuiz.questions.length) {
        return;
    }
    
    currentQuestionIndex = index;
    questionStartTime = Date.now();
    const question = currentQuiz.questions[index];
    
    // 更新进度信息
    updateProgress();
    
    // 添加淡出动画
    const questionCard = document.getElementById('questionCard');
    questionCard.classList.add('fade-out');
    
    setTimeout(() => {
        // 更新题目内容
        questionNumber.textContent = `第 ${index + 1} 题`;
        questionText.textContent = question.question;
        
        // 生成选项
        generateOptions(question, index);
        
        // 更新按钮状态
        updateButtonStates();
        
        // 添加淡入动画
        questionCard.classList.remove('fade-out');
        questionCard.classList.add('fade-in');
        
        setTimeout(() => {
            questionCard.classList.remove('fade-in');
        }, 300);
    }, 150);
}

// 生成选项
function generateOptions(question, questionIndex) {
    optionsContainer.innerHTML = '';
    
    question.options.forEach((option, optionIndex) => {
        const optionElement = document.createElement('div');
        optionElement.className = 'option';
        optionElement.onclick = () => selectOption(questionIndex, optionIndex);
        
        // 如果用户已经选择了答案，显示选中状态
        if (userAnswers[questionIndex] === optionIndex) {
            optionElement.classList.add('selected');
        }
        
        optionElement.innerHTML = `
            <div class="option-letter">${String.fromCharCode(65 + optionIndex)}</div>
            <div class="option-text">${option}</div>
        `;
        
        optionsContainer.appendChild(optionElement);
    });
}

// 选择选项
function selectOption(questionIndex, optionIndex) {
    // 记录用户答案
    userAnswers[questionIndex] = optionIndex;
    
    // 更新选项显示状态
    const options = optionsContainer.querySelectorAll('.option');
    options.forEach((option, index) => {
        option.classList.remove('selected');
        if (index === optionIndex) {
            option.classList.add('selected');
        }
    });
    
    // 启用下一题按钮
    updateButtonStates();
    
    // 如果是最后一题，自动延迟跳转到结果页
    if (questionIndex === currentQuiz.questions.length - 1) {
        setTimeout(() => {
            finishQuiz();
        }, 500);
    }
}

// 更新进度
function updateProgress() {
    const progress = ((currentQuestionIndex + 1) / currentQuiz.questions.length) * 100;
    
    currentQuestionSpan.textContent = currentQuestionIndex + 1;
    progressPercentage.textContent = `${Math.round(progress)}%`;
    progressFill.style.width = `${progress}%`;
}

// 更新按钮状态
function updateButtonStates() {
    // 上一题按钮
    prevBtn.disabled = currentQuestionIndex === 0;
    
    // 下一题按钮
    const hasAnswer = userAnswers[currentQuestionIndex] !== null;
    const isLastQuestion = currentQuestionIndex === currentQuiz.questions.length - 1;
    
    nextBtn.disabled = !hasAnswer;
    nextBtn.textContent = isLastQuestion ? '完成测验' : '下一题';
}

// 上一题
function previousQuestion() {
    if (currentQuestionIndex > 0) {
        showQuestion(currentQuestionIndex - 1);
    }
}

// 下一题
function nextQuestion() {
    if (currentQuestionIndex < currentQuiz.questions.length - 1) {
        showQuestion(currentQuestionIndex + 1);
    } else {
        finishQuiz();
    }
}

// 完成测验
function finishQuiz() {
    // 计算分数
    calculateScore();
    
    // 保存测验结果到 localStorage
    const quizResult = {
        quizType: localStorage.getItem('currentQuizType'),
        quiz: currentQuiz,
        userAnswers: userAnswers,
        score: score,
        totalQuestions: currentQuiz.questions.length,
        completedAt: new Date().toISOString(),
        timeSpent: Date.now() - quizStartTime
    };
    
    localStorage.setItem('quizResult', JSON.stringify(quizResult));
    
    // 跳转到结果页
    window.location.href = 'result.html';
}

// 计算分数
function calculateScore() {
    score = 0;
    
    currentQuiz.questions.forEach((question, index) => {
        const userAnswer = userAnswers[index];
        
        // 对于个性测试，没有正确答案概念
        if (question.correct === -1) {
            score++; // 个性测试每题都算对
        } else if (userAnswer === question.correct) {
            score++;
        }
    });
}

// 退出测验
function quitQuiz() {
    quitModal.classList.add('active');
}

// 关闭退出确认对话框
function closeQuitModal() {
    quitModal.classList.remove('active');
}

// 确认退出
function confirmQuit() {
    // 清除测验数据
    localStorage.removeItem('currentQuizType');
    localStorage.removeItem('quizResult');
    
    // 返回首页
    window.location.href = 'index.html';
}

// 显示加载遮罩
function showLoading() {
    loadingOverlay.classList.remove('hidden');
}

// 隐藏加载遮罩
function hideLoading() {
    loadingOverlay.classList.add('hidden');
}

// 键盘快捷键支持
document.addEventListener('keydown', function(e) {
    // 数字键 1-4 选择选项
    if (e.key >= '1' && e.key <= '4') {
        const optionIndex = parseInt(e.key) - 1;
        const options = optionsContainer.querySelectorAll('.option');
        if (options[optionIndex]) {
            selectOption(currentQuestionIndex, optionIndex);
        }
    }
    
    // 左右箭头键切换题目
    if (e.key === 'ArrowLeft' && !prevBtn.disabled) {
        previousQuestion();
    }
    
    if (e.key === 'ArrowRight' && !nextBtn.disabled) {
        nextQuestion();
    }
    
    // Enter 键下一题
    if (e.key === 'Enter' && !nextBtn.disabled) {
        nextQuestion();
    }
    
    // Escape 键退出测验
    if (e.key === 'Escape') {
        quitQuiz();
    }
});

// 防止页面刷新时丢失进度
window.addEventListener('beforeunload', function(e) {
    if (currentQuiz && userAnswers.some(answer => answer !== null)) {
        e.preventDefault();
        e.returnValue = '你确定要离开吗？测验进度将会丢失。';
    }
});

// 处理浏览器后退按钮
window.addEventListener('popstate', function(e) {
    if (currentQuiz) {
        e.preventDefault();
        quitQuiz();
    }
});

// 工具函数：格式化时间
function formatQuizTime(milliseconds) {
    const seconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    
    if (minutes > 0) {
        return `${minutes}分${remainingSeconds}秒`;
    } else {
        return `${remainingSeconds}秒`;
    }
}

// 工具函数：获取性格类型结果（用于个性测试）
function getPersonalityResult(answers) {
    if (!currentQuiz || currentQuiz.category !== '个性测试') {
        return null;
    }
    
    const personalityTypes = {};
    
    answers.forEach((answerIndex, questionIndex) => {
        if (answerIndex !== null) {
            const question = currentQuiz.questions[questionIndex];
            if (question.personality && question.personality[answerIndex]) {
                const type = question.personality[answerIndex];
                personalityTypes[type] = (personalityTypes[type] || 0) + 1;
            }
        }
    });
    
    // 找出最多的性格类型
    let maxCount = 0;
    let dominantType = '平衡型';
    
    for (const [type, count] of Object.entries(personalityTypes)) {
        if (count > maxCount) {
            maxCount = count;
            dominantType = type;
        }
    }
    
    return {
        type: dominantType,
        distribution: personalityTypes
    };
}

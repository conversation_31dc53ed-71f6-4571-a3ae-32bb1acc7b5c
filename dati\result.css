/* 结果页面专用样式 */

/* 结果导航栏 */
.result-nav-info {
    display: flex;
    align-items: center;
}

.result-status {
    background-color: var(--success-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

/* 结果主体 */
.result-main {
    min-height: calc(100vh - 80px);
    padding: var(--spacing-8) 0;
    background: linear-gradient(135deg, var(--gray-50) 0%, var(--white) 100%);
}

/* 结果头部 */
.result-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.result-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-4);
    animation: bounce 1s ease-in-out;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.result-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.result-subtitle {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
}

/* 结果卡片 */
.result-card {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--white);
    border-radius: var(--border-radius-2xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--gray-200);
}

/* 分数展示 */
.score-section {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-8);
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.score-circle {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background: conic-gradient(var(--primary-color) 0deg, var(--gray-200) 0deg);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}

.score-circle::before {
    content: '';
    position: absolute;
    width: 90px;
    height: 90px;
    background-color: var(--white);
    border-radius: 50%;
}

.score-number {
    position: relative;
    z-index: 1;
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--primary-color);
}

.score-total {
    position: relative;
    z-index: 1;
    font-size: var(--font-size-base);
    color: var(--gray-500);
    margin-top: -5px;
}

.score-info {
    text-align: left;
}

.score-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.score-percentage {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: var(--spacing-3);
}

.score-description {
    color: var(--gray-600);
    line-height: 1.5;
    max-width: 300px;
}

/* 详细分析 */
.analysis-section,
.personality-section,
.stats-section,
.review-section {
    margin-bottom: var(--spacing-8);
    padding-bottom: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.analysis-section:last-of-type,
.personality-section:last-of-type,
.stats-section:last-of-type,
.review-section:last-of-type {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.analysis-title,
.personality-title,
.stats-title,
.review-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.analysis-content {
    color: var(--gray-700);
    line-height: 1.6;
}

/* 个性测试结果 */
.personality-result {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
}

.personality-type {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-3);
}

.personality-description {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.personality-traits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-3);
}

.trait-item {
    background-color: var(--white);
    border-radius: var(--border-radius);
    padding: var(--spacing-3);
    text-align: center;
}

.trait-name {
    font-weight: 500;
    color: var(--gray-800);
    margin-bottom: var(--spacing-1);
}

.trait-score {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    font-weight: 600;
}

/* 测验统计 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-4);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-3);
    background-color: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
}

.stat-icon {
    font-size: var(--font-size-2xl);
}

.stat-info {
    flex: 1;
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    margin-bottom: var(--spacing-1);
}

.stat-value {
    font-weight: 600;
    color: var(--gray-900);
}

/* 题目回顾 */
.review-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.review-question {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
    border-left: 4px solid var(--gray-300);
}

.review-question.correct {
    border-left-color: var(--success-color);
    background-color: #f0fdf4;
}

.review-question.incorrect {
    border-left-color: var(--danger-color);
    background-color: #fef2f2;
}

.review-question-text {
    font-weight: 500;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.review-answers {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.review-answer {
    font-size: var(--font-size-sm);
    padding: var(--spacing-1) 0;
}

.review-answer.user-answer {
    color: var(--primary-color);
    font-weight: 500;
}

.review-answer.correct-answer {
    color: var(--success-color);
    font-weight: 500;
}

.review-answer.incorrect-answer {
    color: var(--danger-color);
    font-weight: 500;
}

/* 操作按钮 */
.result-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    flex-wrap: wrap;
    margin-top: var(--spacing-8);
}

.btn-outline {
    background-color: transparent;
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
}

.btn-outline:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

/* 分享模态框 */
.modal-close {
    position: absolute;
    top: var(--spacing-4);
    right: var(--spacing-4);
    background: none;
    border: none;
    font-size: var(--font-size-xl);
    color: var(--gray-400);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.share-preview {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-4);
    margin-bottom: var(--spacing-6);
    text-align: center;
}

.share-options {
    display: flex;
    gap: var(--spacing-3);
    justify-content: center;
    flex-wrap: wrap;
}

.share-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    padding: var(--spacing-3) var(--spacing-4);
    background-color: var(--gray-100);
    color: var(--gray-700);
    border: none;
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.share-btn:hover {
    background-color: var(--gray-200);
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .result-main {
        padding: var(--spacing-4) 0;
    }
    
    .result-card {
        padding: var(--spacing-6);
    }
    
    .score-section {
        flex-direction: column;
        gap: var(--spacing-6);
        text-align: center;
    }
    
    .score-info {
        text-align: center;
    }
    
    .score-circle {
        width: 100px;
        height: 100px;
    }
    
    .score-circle::before {
        width: 75px;
        height: 75px;
    }
    
    .score-number {
        font-size: var(--font-size-2xl);
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .result-actions {
        flex-direction: column;
    }
    
    .result-actions .btn {
        width: 100%;
    }
    
    .share-options {
        flex-direction: column;
    }
    
    .share-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .result-title {
        font-size: var(--font-size-2xl);
    }
    
    .result-subtitle {
        font-size: var(--font-size-base);
    }
    
    .score-title {
        font-size: var(--font-size-xl);
    }
    
    .score-percentage {
        font-size: var(--font-size-lg);
    }
    
    .analysis-title,
    .personality-title,
    .stats-title,
    .review-title {
        font-size: var(--font-size-lg);
    }
    
    .stat-item {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-2);
    }
}

/* 动画效果 */
.score-circle {
    animation: scoreReveal 1.5s ease-out;
}

@keyframes scoreReveal {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.result-card {
    animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 工具类 */
.text-success {
    color: var(--success-color) !important;
}

.text-danger {
    color: var(--danger-color) !important;
}

.text-warning {
    color: var(--warning-color) !important;
}

.bg-success {
    background-color: var(--success-color) !important;
}

.bg-danger {
    background-color: var(--danger-color) !important;
}

.bg-warning {
    background-color: var(--warning-color) !important;
}

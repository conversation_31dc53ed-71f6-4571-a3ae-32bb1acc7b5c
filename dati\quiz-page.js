// 测验页面JavaScript

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeQuizPage();
});

// 初始化测验页面
function initializeQuizPage() {
    // 初始化评论功能
    initializeComments();
    
    // 初始化分享功能
    initializeShareButtons();
    
    // 初始化相关测验点击
    initializeRelatedQuizzes();
    
    // 初始化热门测验点击
    initializePopularQuizzes();
}

// 开始测验
function startQuizTest() {
    // 显示确认对话框
    if (confirm('准备好开始测验了吗？测验一旦开始就不能暂停。')) {
        // 这里可以跳转到实际的测验页面
        alert('测验即将开始！这里可以跳转到测验界面。');
        // window.location.href = 'quiz-test.html';
    }
}

// 初始化评论功能
function initializeComments() {
    const commentSubmitBtn = document.querySelector('.comment-submit-btn');
    const commentInput = document.querySelector('.comment-input');
    
    if (commentSubmitBtn && commentInput) {
        commentSubmitBtn.addEventListener('click', function() {
            const commentText = commentInput.value.trim();
            if (commentText) {
                addComment(commentText);
                commentInput.value = '';
            } else {
                alert('请输入评论内容');
            }
        });
    }
    
    // 初始化点赞和回复按钮
    initializeCommentActions();
}

// 添加新评论
function addComment(text) {
    const commentsList = document.querySelector('.comments-list');
    if (!commentsList) return;
    
    const newComment = createCommentElement('你', '刚刚', text);
    commentsList.insertBefore(newComment, commentsList.firstChild);
    
    // 更新评论数量
    updateCommentCount();
}

// 创建评论元素
function createCommentElement(author, time, text) {
    const comment = document.createElement('div');
    comment.className = 'comment';
    
    comment.innerHTML = `
        <div class="comment-avatar">
            <img src="https://via.placeholder.com/40x40/6366f1/ffffff?text=U" alt="用户头像">
        </div>
        <div class="comment-content">
            <div class="comment-header">
                <span class="comment-author">${author}</span>
                <span class="comment-time">${time}</span>
            </div>
            <p class="comment-text">${text}</p>
            <div class="comment-actions">
                <button class="comment-like">👍 0</button>
                <button class="comment-reply">回复</button>
            </div>
        </div>
    `;
    
    // 为新评论添加事件监听器
    const likeBtn = comment.querySelector('.comment-like');
    const replyBtn = comment.querySelector('.comment-reply');
    
    likeBtn.addEventListener('click', function() {
        handleCommentLike(this);
    });
    
    replyBtn.addEventListener('click', function() {
        handleCommentReply(this);
    });
    
    return comment;
}

// 初始化评论操作
function initializeCommentActions() {
    const likeButtons = document.querySelectorAll('.comment-like');
    const replyButtons = document.querySelectorAll('.comment-reply');
    
    likeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            handleCommentLike(this);
        });
    });
    
    replyButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            handleCommentReply(this);
        });
    });
}

// 处理评论点赞
function handleCommentLike(button) {
    const currentText = button.textContent;
    const currentCount = parseInt(currentText.match(/\d+/)[0]);
    const newCount = currentCount + 1;
    
    button.textContent = `👍 ${newCount}`;
    button.style.color = 'var(--primary-color)';
    button.disabled = true;
}

// 处理评论回复
function handleCommentReply(button) {
    const commentContent = button.closest('.comment-content');
    const author = commentContent.querySelector('.comment-author').textContent;
    const commentInput = document.querySelector('.comment-input');
    
    if (commentInput) {
        commentInput.value = `@${author} `;
        commentInput.focus();
    }
}

// 更新评论数量
function updateCommentCount() {
    const commentsTitle = document.querySelector('.comments-section h3');
    const commentsList = document.querySelector('.comments-list');
    
    if (commentsTitle && commentsList) {
        const count = commentsList.children.length;
        commentsTitle.textContent = `💬 参与讨论 (${count}条评论)`;
    }
}

// 初始化分享功能
function initializeShareButtons() {
    const shareButtons = document.querySelectorAll('.share-btn');
    
    shareButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            const platform = this.classList.contains('facebook') ? 'Facebook' :
                           this.classList.contains('twitter') ? 'Twitter' : '微信';
            
            handleShare(platform);
        });
    });
}

// 处理分享
function handleShare(platform) {
    const url = window.location.href;
    const title = document.querySelector('.quiz-title').textContent;
    
    switch(platform) {
        case 'Facebook':
            window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
            break;
        case 'Twitter':
            window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
            break;
        case '微信':
            // 微信分享通常需要特殊处理，这里只是示例
            alert('请复制链接到微信分享：' + url);
            break;
    }
}

// 初始化相关测验
function initializeRelatedQuizzes() {
    const relatedQuizCards = document.querySelectorAll('.related-quiz-card');
    
    relatedQuizCards.forEach(card => {
        card.addEventListener('click', function() {
            const title = this.querySelector('h4').textContent;
            alert(`即将跳转到：${title}`);
            // 这里可以实现实际的跳转逻辑
        });
    });
}

// 初始化热门测验
function initializePopularQuizzes() {
    const popularQuizItems = document.querySelectorAll('.popular-quiz-item');
    
    popularQuizItems.forEach(item => {
        item.addEventListener('click', function() {
            const title = this.querySelector('h4').textContent;
            alert(`即将跳转到：${title}`);
            // 这里可以实现实际的跳转逻辑
        });
    });
}

// 广告按钮点击
document.addEventListener('click', function(e) {
    if (e.target.classList.contains('ad-btn')) {
        alert('跳转到学习资源页面');
        // 这里可以实现实际的跳转逻辑
    }
});

// 滚动到评论区
function scrollToComments() {
    const commentsSection = document.querySelector('.comments-section');
    if (commentsSection) {
        commentsSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 导出函数供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        startQuizTest,
        addComment,
        handleShare,
        scrollToComments
    };
}

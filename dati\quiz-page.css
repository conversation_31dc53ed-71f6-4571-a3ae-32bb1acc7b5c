/* 测验页面样式 */

/* 主要布局 */
.quiz-main {
    padding: var(--spacing-6) 0;
    background-color: var(--gray-50);
    min-height: calc(100vh - 72px);
}

.quiz-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    max-width: 1400px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .quiz-layout {
        grid-template-columns: 2fr 1fr;
    }
}

/* 左侧主要内容 */
.quiz-content {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
}

/* 测验标题区域 */
.quiz-header {
    margin-bottom: var(--spacing-8);
}

.quiz-breadcrumb {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
}

.quiz-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
}

.quiz-breadcrumb a:hover {
    text-decoration: underline;
}

.quiz-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    line-height: 1.2;
}

.quiz-meta {
    display: flex;
    gap: var(--spacing-6);
    flex-wrap: wrap;
}

.quiz-meta span {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    background: var(--gray-100);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
}

/* 测验图片 */
.quiz-image-container {
    margin-bottom: var(--spacing-8);
}

.quiz-main-image {
    width: 100%;
    height: 300px;
    object-fit: cover;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
}

/* 测验描述 */
.quiz-description {
    margin-bottom: var(--spacing-8);
}

.quiz-description h2 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.quiz-description h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin: var(--spacing-6) 0 var(--spacing-3) 0;
}

.quiz-description p {
    color: var(--gray-700);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.quiz-description ul {
    list-style: none;
    padding: 0;
}

.quiz-description li {
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
    padding-left: var(--spacing-6);
    position: relative;
}

/* 开始测验按钮 */
.quiz-start-section {
    text-align: center;
    margin-bottom: var(--spacing-8);
    padding: var(--spacing-6);
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    border-radius: var(--border-radius-lg);
}

.start-quiz-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-2);
    background: var(--white);
    color: var(--primary-color);
    border: none;
    padding: var(--spacing-4) var(--spacing-8);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-lg);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    box-shadow: var(--shadow);
}

.start-quiz-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.quiz-note {
    margin-top: var(--spacing-4);
    color: var(--white);
    font-size: var(--font-size-sm);
}

/* 相关测验 */
.related-quizzes {
    margin-bottom: var(--spacing-8);
}

.related-quizzes h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.related-quiz-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

@media (min-width: 640px) {
    .related-quiz-grid {
        grid-template-columns: 1fr 1fr;
    }
}

.related-quiz-card {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
}

.related-quiz-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.related-quiz-card img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.related-quiz-content h4 {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.related-quiz-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* 评论区域 */
.comments-section {
    border-top: 1px solid var(--gray-200);
    padding-top: var(--spacing-8);
}

.comments-section h3 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-6);
}

.add-comment {
    display: flex;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-6);
}

.comment-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-form {
    flex: 1;
}

.comment-input {
    width: 100%;
    min-height: 80px;
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-family: inherit;
    font-size: var(--font-size-base);
    resize: vertical;
}

.comment-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.comment-submit-btn {
    margin-top: var(--spacing-2);
    background: var(--primary-color);
    color: var(--white);
    border: none;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.comment-submit-btn:hover {
    background: var(--primary-hover);
}

/* 评论列表 */
.comments-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.comment {
    display: flex;
    gap: var(--spacing-3);
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-2);
}

.comment-author {
    font-weight: 600;
    color: var(--gray-900);
}

.comment-time {
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.comment-text {
    color: var(--gray-700);
    line-height: 1.5;
    margin-bottom: var(--spacing-2);
}

.comment-actions {
    display: flex;
    gap: var(--spacing-4);
}

.comment-like,
.comment-reply {
    background: none;
    border: none;
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.comment-like:hover,
.comment-reply:hover {
    color: var(--primary-color);
}

/* 右侧边栏 */
.quiz-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.sidebar-widget {
    background: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow-lg);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

/* 测验统计 */
.quiz-stats {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.stat-item {
    text-align: center;
    padding: var(--spacing-4);
    background: var(--gray-50);
    border-radius: var(--border-radius-lg);
}

.stat-number {
    display: block;
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: var(--spacing-1);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
}

/* 热门测验 */
.popular-quizzes {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.popular-quiz-item {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-3);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-fast);
}

.popular-quiz-item:hover {
    background: var(--gray-50);
}

.popular-quiz-item img {
    width: 50px;
    height: 50px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.popular-quiz-content h4 {
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.popular-quiz-participants {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 最新活动 */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.activity-item {
    display: flex;
    gap: var(--spacing-3);
    padding: var(--spacing-2);
}

.activity-icon {
    font-size: var(--font-size-lg);
    width: 32px;
    text-align: center;
}

.activity-content p {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    margin-bottom: var(--spacing-1);
}

.activity-time {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 分享按钮 */
.share-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.share-btn {
    padding: var(--spacing-2) var(--spacing-4);
    border: none;
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.share-btn.facebook {
    background: #1877f2;
    color: var(--white);
}

.share-btn.twitter {
    background: #1da1f2;
    color: var(--white);
}

.share-btn.wechat {
    background: #07c160;
    color: var(--white);
}

.share-btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 广告位 */
.ad-widget {
    background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
    color: var(--white);
}

.ad-widget .widget-title {
    color: var(--white);
}

.ad-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-2);
}

.ad-content p {
    margin-bottom: var(--spacing-4);
    opacity: 0.9;
}

.ad-btn {
    background: var(--white);
    color: var(--primary-color);
    border: none;
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
}

.ad-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

/* 响应式设计 */
@media (max-width: 1023px) {
    .quiz-layout {
        grid-template-columns: 1fr;
    }

    .quiz-sidebar {
        order: -1;
    }
}

@media (max-width: 767px) {
    .quiz-content {
        padding: var(--spacing-4);
    }

    .quiz-title {
        font-size: var(--font-size-2xl);
    }

    .quiz-meta {
        flex-direction: column;
        gap: var(--spacing-2);
    }

    .related-quiz-grid {
        grid-template-columns: 1fr;
    }

    .sidebar-widget {
        padding: var(--spacing-4);
    }
}

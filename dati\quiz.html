<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测验进行中 - QuizMaster</title>
    <meta name="description" content="正在进行测验，测试你的知识水平">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="quiz.css">
</head>
<body>
    <!-- 导航栏 -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <img src="https://via.placeholder.com/40x40/6366f1/ffffff?text=Q" alt="QuizMaster Logo">
                        <span>QuizMaster</span>
                    </a>
                </div>
                
                <div class="quiz-nav-info">
                    <span class="quiz-category" id="quizCategory">测验进行中</span>
                    <button class="quit-btn" onclick="quitQuiz()">退出测验</button>
                </div>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="quiz-main">
        <div class="container">
            <!-- 测验头部信息 -->
            <div class="quiz-header">
                <div class="quiz-info">
                    <h1 class="quiz-title" id="quizTitle">加载中...</h1>
                    <p class="quiz-description" id="quizDescription">正在加载测验内容...</p>
                </div>
                
                <!-- 进度条 -->
                <div class="progress-container">
                    <div class="progress-info">
                        <span class="progress-text">
                            第 <span id="currentQuestion">1</span> 题，共 <span id="totalQuestions">5</span> 题
                        </span>
                        <span class="progress-percentage" id="progressPercentage">20%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                </div>
            </div>

            <!-- 测验内容 -->
            <div class="quiz-content">
                <!-- 题目卡片 -->
                <div class="question-card" id="questionCard">
                    <div class="question-number">
                        <span id="questionNumber">第 1 题</span>
                    </div>
                    
                    <div class="question-text" id="questionText">
                        正在加载题目...
                    </div>
                    
                    <!-- 选项列表 -->
                    <div class="options-container" id="optionsContainer">
                        <!-- 选项将通过 JavaScript 动态生成 -->
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="question-actions">
                        <button class="btn btn-secondary" id="prevBtn" onclick="previousQuestion()" disabled>
                            上一题
                        </button>
                        <button class="btn btn-primary" id="nextBtn" onclick="nextQuestion()" disabled>
                            下一题
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p>正在加载测验...</p>
        </div>
    </div>

    <!-- 确认退出对话框 -->
    <div class="modal-overlay" id="quitModal">
        <div class="modal">
            <div class="modal-header">
                <h3>确认退出</h3>
            </div>
            <div class="modal-body">
                <p>你确定要退出当前测验吗？你的进度将会丢失。</p>
            </div>
            <div class="modal-actions">
                <button class="btn btn-secondary" onclick="closeQuitModal()">取消</button>
                <button class="btn btn-danger" onclick="confirmQuit()">确认退出</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="quiz.js"></script>
</body>
</html>

/* 关于页面专用样式 */

/* 关于页面主体 */
.about-main {
    padding-top: 0;
}

/* 英雄区域 */
.about-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--spacing-20) 0;
    text-align: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 使命愿景区域 */
.mission-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.mission-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .mission-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.mission-item {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
}

.mission-item:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.mission-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-4);
}

.mission-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.mission-description {
    color: var(--gray-600);
    line-height: 1.6;
}

/* 团队区域 */
.team-section {
    padding: var(--spacing-20) 0;
}

.team-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 640px) {
    .team-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .team-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.team-member {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
}

.team-member:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.member-avatar {
    margin-bottom: var(--spacing-4);
}

.member-avatar img {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    object-fit: cover;
    border: 4px solid var(--gray-100);
}

.member-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.member-role {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: var(--spacing-3);
}

.member-bio {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* 发展历程 */
.timeline-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: var(--primary-color);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: var(--spacing-8);
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-date {
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-4);
    border-radius: var(--border-radius-full);
    font-weight: 600;
    min-width: 80px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-content {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    flex: 1;
    margin: 0 var(--spacing-6);
    max-width: 300px;
}

.timeline-content h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.timeline-content p {
    color: var(--gray-600);
    line-height: 1.5;
}

/* 移动端时间线 */
@media (max-width: 768px) {
    .timeline::before {
        left: 20px;
    }
    
    .timeline-item {
        flex-direction: row !important;
        padding-left: var(--spacing-12);
    }
    
    .timeline-date {
        position: absolute;
        left: 0;
        min-width: 60px;
        font-size: var(--font-size-sm);
        padding: var(--spacing-1) var(--spacing-2);
    }
    
    .timeline-content {
        margin: 0;
        max-width: none;
    }
}

/* 技术栈 */
.tech-section {
    padding: var(--spacing-20) 0;
}

.tech-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .tech-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.tech-category {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    text-align: center;
}

.tech-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.tech-items {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    justify-content: center;
}

.tech-item {
    background-color: var(--gray-100);
    color: var(--gray-700);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    transition: var(--transition-fast);
}

.tech-item:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* 联系信息 */
.contact-info-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .contact-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.contact-item {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    text-align: center;
    box-shadow: var(--shadow);
}

.contact-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-3);
}

.contact-item h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.contact-item p {
    color: var(--gray-600);
    line-height: 1.5;
}

/* 社交链接 */
.social-links {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2);
}

.social-link {
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition-fast);
}

.social-link:hover {
    color: var(--white);
}

/* 页脚版权信息 */
.footer-copyright p {
    margin-bottom: var(--spacing-1);
}

/* 动画效果 */
.mission-item,
.team-member,
.timeline-item,
.tech-category,
.contact-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.mission-item.fade-in,
.team-member.fade-in,
.timeline-item.fade-in,
.tech-category.fade-in,
.contact-item.fade-in {
    opacity: 1;
    transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .about-hero {
        padding: var(--spacing-12) 0;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .mission-section,
    .team-section,
    .timeline-section,
    .tech-section,
    .contact-info-section {
        padding: var(--spacing-12) 0;
    }
    
    .member-avatar img {
        width: 100px;
        height: 100px;
    }
    
    .timeline-content {
        padding: var(--spacing-4);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .mission-item,
    .team-member,
    .tech-category,
    .contact-item {
        padding: var(--spacing-4);
    }
    
    .member-avatar img {
        width: 80px;
        height: 80px;
    }
    
    .mission-icon {
        font-size: 2rem;
    }
    
    .contact-icon {
        font-size: 2rem;
    }
}

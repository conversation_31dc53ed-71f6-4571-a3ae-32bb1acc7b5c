// 关于页面专用脚本

// DOM 元素
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const navMenu = document.getElementById('navMenu');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeAboutPage();
});

// 初始化关于页面
function initializeAboutPage() {
    // 移动端菜单切换
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 添加滚动动画
    addScrollAnimations();
    
    // 添加团队成员悬停效果
    addTeamMemberEffects();
    
    // 添加技术栈交互效果
    addTechStackEffects();
    
    // 平滑滚动到锚点
    addSmoothScrolling();
}

// 切换移动端菜单
function toggleMobileMenu() {
    navMenu.classList.toggle('active');
    
    // 切换汉堡菜单图标动画
    const spans = mobileMenuBtn.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) span.style.opacity = '0';
            if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            span.style.transform = '';
            span.style.opacity = '';
        }
    });
}

// 添加滚动动画
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                
                // 为时间线项目添加延迟动画
                if (entry.target.classList.contains('timeline-item')) {
                    const timelineItems = document.querySelectorAll('.timeline-item');
                    const index = Array.from(timelineItems).indexOf(entry.target);
                    entry.target.style.animationDelay = `${index * 0.2}s`;
                }
                
                // 为团队成员添加延迟动画
                if (entry.target.classList.contains('team-member')) {
                    const teamMembers = document.querySelectorAll('.team-member');
                    const index = Array.from(teamMembers).indexOf(entry.target);
                    entry.target.style.animationDelay = `${index * 0.1}s`;
                }
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll(
        '.mission-item, .team-member, .timeline-item, .tech-category, .contact-item'
    );
    
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// 添加团队成员悬停效果
function addTeamMemberEffects() {
    const teamMembers = document.querySelectorAll('.team-member');
    
    teamMembers.forEach(member => {
        const avatar = member.querySelector('.member-avatar img');
        
        member.addEventListener('mouseenter', function() {
            avatar.style.transform = 'scale(1.1)';
            avatar.style.transition = 'transform 0.3s ease';
        });
        
        member.addEventListener('mouseleave', function() {
            avatar.style.transform = 'scale(1)';
        });
    });
}

// 添加技术栈交互效果
function addTechStackEffects() {
    const techItems = document.querySelectorAll('.tech-item');
    
    techItems.forEach(item => {
        item.addEventListener('click', function() {
            // 简单的点击效果
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // 可以在这里添加更多交互，比如显示技术详情
            console.log('点击了技术:', this.textContent);
        });
    });
}

// 添加平滑滚动
function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 添加数字计数动画
function animateNumbers() {
    const numberElements = document.querySelectorAll('.stat-number');
    
    numberElements.forEach(element => {
        const finalNumber = parseInt(element.textContent);
        let currentNumber = 0;
        const increment = finalNumber / 50; // 50帧动画
        
        const animation = setInterval(() => {
            currentNumber += increment;
            if (currentNumber >= finalNumber) {
                currentNumber = finalNumber;
                clearInterval(animation);
            }
            element.textContent = Math.floor(currentNumber);
        }, 50);
    });
}

// 添加时间线交互效果
function addTimelineEffects() {
    const timelineItems = document.querySelectorAll('.timeline-item');
    
    timelineItems.forEach(item => {
        item.addEventListener('mouseenter', function() {
            const content = this.querySelector('.timeline-content');
            content.style.transform = 'scale(1.05)';
            content.style.transition = 'transform 0.3s ease';
        });
        
        item.addEventListener('mouseleave', function() {
            const content = this.querySelector('.timeline-content');
            content.style.transform = 'scale(1)';
        });
    });
}

// 添加联系信息点击效果
function addContactEffects() {
    const contactItems = document.querySelectorAll('.contact-item');
    
    contactItems.forEach(item => {
        item.addEventListener('click', function() {
            const icon = this.querySelector('.contact-icon');
            
            // 添加点击动画
            icon.style.transform = 'scale(1.2)';
            icon.style.transition = 'transform 0.2s ease';
            
            setTimeout(() => {
                icon.style.transform = 'scale(1)';
            }, 200);
            
            // 根据联系方式类型执行不同操作
            const title = this.querySelector('h4').textContent;
            
            if (title.includes('邮箱')) {
                // 复制邮箱地址
                const email = this.querySelector('p').textContent.split('\n')[0];
                copyToClipboard(email);
                showToast('邮箱地址已复制到剪贴板');
            } else if (title.includes('电话')) {
                // 复制电话号码
                const phone = this.querySelector('p').textContent.split('\n')[0];
                copyToClipboard(phone);
                showToast('电话号码已复制到剪贴板');
            } else if (title.includes('地址')) {
                // 复制地址
                const address = this.querySelector('p').textContent.replace('\n', ' ');
                copyToClipboard(address);
                showToast('地址已复制到剪贴板');
            }
        });
    });
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).catch(() => {
            fallbackCopyText(text);
        });
    } else {
        fallbackCopyText(text);
    }
}

// 备用复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.error('复制失败:', err);
    }
    document.body.removeChild(textArea);
}

// 显示提示消息
function showToast(message) {
    // 创建提示元素
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--primary-color);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 添加键盘导航支持
function addKeyboardNavigation() {
    document.addEventListener('keydown', function(e) {
        // Tab键导航增强
        if (e.key === 'Tab') {
            const focusableElements = document.querySelectorAll(
                'a, button, input, textarea, select, [tabindex]:not([tabindex="-1"])'
            );
            
            // 添加焦点样式
            focusableElements.forEach(element => {
                element.addEventListener('focus', function() {
                    this.style.outline = '2px solid var(--primary-color)';
                    this.style.outlineOffset = '2px';
                });
                
                element.addEventListener('blur', function() {
                    this.style.outline = '';
                    this.style.outlineOffset = '';
                });
            });
        }
    });
}

// 初始化所有效果
setTimeout(() => {
    addTimelineEffects();
    addContactEffects();
    addKeyboardNavigation();
}, 1000);

// 页面滚动时的视差效果
window.addEventListener('scroll', function() {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('.about-hero');
    
    parallaxElements.forEach(element => {
        const speed = 0.5;
        element.style.transform = `translateY(${scrolled * speed}px)`;
    });
});

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

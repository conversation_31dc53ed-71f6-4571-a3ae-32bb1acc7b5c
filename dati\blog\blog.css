/* 博客页面专用样式 */

/* 博客主体 */
.blog-main {
    padding-top: 0;
}

/* 英雄区域 */
.blog-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--spacing-16) 0;
    text-align: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 博客内容区域 */
.blog-content {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.blog-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 1024px) {
    .blog-layout {
        grid-template-columns: 2fr 1fr;
    }
}

/* 文章区域 */
.blog-articles {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-8);
}

/* 特色文章 */
.featured-article {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
}

.featured-article:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.featured-article .article-image {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.featured-article .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.featured-article:hover .article-image img {
    transform: scale(1.05);
}

.article-category {
    position: absolute;
    top: var(--spacing-4);
    left: var(--spacing-4);
    background-color: var(--primary-color);
    color: var(--white);
    padding: var(--spacing-1) var(--spacing-3);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.featured-article .article-content {
    padding: var(--spacing-8);
}

.featured-article .article-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.featured-article .article-title a {
    color: var(--gray-900);
    text-decoration: none;
    transition: var(--transition-fast);
}

.featured-article .article-title a:hover {
    color: var(--primary-color);
}

.article-excerpt {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-4);
}

.article-meta {
    display: flex;
    gap: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
    flex-wrap: wrap;
}

.article-meta span {
    display: flex;
    align-items: center;
    gap: var(--spacing-1);
}

/* 文章网格 */
.articles-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
}

@media (min-width: 640px) {
    .articles-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .articles-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* 文章卡片 */
.article-card {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
}

.article-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.article-card .article-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.article-card .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-normal);
}

.article-card:hover .article-image img {
    transform: scale(1.05);
}

.article-card .article-content {
    padding: var(--spacing-6);
}

.article-card .article-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-3);
    line-height: 1.4;
}

.article-card .article-title a {
    color: var(--gray-900);
    text-decoration: none;
    transition: var(--transition-fast);
}

.article-card .article-title a:hover {
    color: var(--primary-color);
}

.article-card .article-excerpt {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-3);
}

/* 分页 */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: var(--spacing-2);
    margin-top: var(--spacing-8);
}

.pagination-btn {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-700);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.pagination-btn:hover {
    background-color: var(--gray-50);
    border-color: var(--gray-400);
}

.pagination-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.pagination-dots {
    color: var(--gray-400);
    padding: 0 var(--spacing-2);
}

/* 侧边栏 */
.blog-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.sidebar-widget {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--primary-color);
}

/* 搜索小部件 */
.search-widget {
    display: flex;
    gap: var(--spacing-2);
}

.search-input {
    flex: 1;
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.search-btn {
    padding: var(--spacing-2);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-btn:hover {
    background-color: var(--primary-hover);
}

/* 分类列表 */
.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-list li {
    margin-bottom: var(--spacing-2);
}

.category-link {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-2) var(--spacing-3);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.category-link:hover {
    background-color: var(--gray-50);
    color: var(--primary-color);
}

.category-count {
    font-size: var(--font-size-xs);
    color: var(--gray-400);
}

/* 热门文章 */
.popular-posts {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.popular-post {
    display: flex;
    gap: var(--spacing-3);
}

.post-image {
    flex-shrink: 0;
}

.post-image img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.post-content {
    flex: 1;
}

.post-title {
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-1);
    line-height: 1.4;
}

.post-title a {
    color: var(--gray-800);
    text-decoration: none;
    transition: var(--transition-fast);
}

.post-title a:hover {
    color: var(--primary-color);
}

.post-date {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 标签云 */
.tag-cloud {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
}

.tag {
    padding: var(--spacing-1) var(--spacing-2);
    background-color: var(--gray-100);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-xs);
    transition: var(--transition-fast);
}

.tag:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* 订阅小部件 */
.newsletter-widget p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
}

.newsletter-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-3);
}

.newsletter-input {
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
}

.newsletter-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.newsletter-btn {
    padding: var(--spacing-2) var(--spacing-4);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
}

.newsletter-btn:hover {
    background-color: var(--primary-hover);
}

/* 响应式设计 */
@media (max-width: 1023px) {
    .blog-sidebar {
        order: -1;
    }
    
    .sidebar-widget {
        padding: var(--spacing-4);
    }
}

@media (max-width: 768px) {
    .blog-hero {
        padding: var(--spacing-12) 0;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .blog-content {
        padding: var(--spacing-12) 0;
    }
    
    .featured-article .article-image {
        height: 250px;
    }
    
    .featured-article .article-content {
        padding: var(--spacing-6);
    }
    
    .featured-article .article-title {
        font-size: var(--font-size-xl);
    }
    
    .articles-grid {
        grid-template-columns: 1fr;
    }
    
    .article-card .article-content {
        padding: var(--spacing-4);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .featured-article .article-image {
        height: 200px;
    }
    
    .featured-article .article-content {
        padding: var(--spacing-4);
    }
    
    .featured-article .article-title {
        font-size: var(--font-size-lg);
    }
    
    .article-meta {
        flex-direction: column;
        gap: var(--spacing-2);
    }
    
    .pagination {
        flex-wrap: wrap;
    }
}

/* 动画效果 */
.article-card,
.featured-article,
.sidebar-widget {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.article-card.fade-in,
.featured-article.fade-in,
.sidebar-widget.fade-in {
    opacity: 1;
    transform: translateY(0);
}

/* 加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--spacing-8);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--gray-200);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 搜索结果高亮 */
.search-highlight {
    background-color: yellow;
    padding: 1px 2px;
    border-radius: 2px;
}

/* 无结果状态 */
.no-results {
    text-align: center;
    padding: var(--spacing-12);
    color: var(--gray-500);
}

.no-results h3 {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
}

.no-results p {
    margin-bottom: var(--spacing-6);
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: var(--spacing-6);
    right: var(--spacing-6);
    background-color: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-fast);
    opacity: 0;
    visibility: hidden;
    z-index: 100;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    background-color: var(--primary-hover);
    transform: translateY(-2px);
}

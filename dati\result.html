<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测验结果 - QuizMaster</title>
    <meta name="description" content="查看你的测验结果和详细分析">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="result.css">
</head>
<body>
    <!-- 导航栏 -->
    <header class="header">
        <nav class="nav">
            <div class="nav-container">
                <div class="nav-brand">
                    <a href="index.html" class="logo">
                        <img src="https://via.placeholder.com/40x40/6366f1/ffffff?text=Q" alt="QuizMaster Logo">
                        <span>QuizMaster</span>
                    </a>
                </div>
                
                <div class="result-nav-info">
                    <span class="result-status" id="resultStatus">测验完成</span>
                </div>
            </div>
        </nav>
    </header>

    <!-- 主要内容 -->
    <main class="result-main">
        <div class="container">
            <!-- 结果头部 -->
            <div class="result-header">
                <div class="result-icon" id="resultIcon">
                    🎉
                </div>
                <h1 class="result-title" id="resultTitle">恭喜完成测验！</h1>
                <p class="result-subtitle" id="resultSubtitle">查看你的详细结果</p>
            </div>

            <!-- 结果卡片 -->
            <div class="result-card">
                <!-- 分数展示 -->
                <div class="score-section">
                    <div class="score-circle" id="scoreCircle">
                        <div class="score-number" id="scoreNumber">0</div>
                        <div class="score-total" id="scoreTotal">/ 5</div>
                    </div>
                    <div class="score-info">
                        <h2 class="score-title" id="scoreTitle">你的得分</h2>
                        <p class="score-percentage" id="scorePercentage">0%</p>
                        <p class="score-description" id="scoreDescription">加载中...</p>
                    </div>
                </div>

                <!-- 详细分析 -->
                <div class="analysis-section" id="analysisSection">
                    <h3 class="analysis-title">详细分析</h3>
                    <div class="analysis-content" id="analysisContent">
                        <!-- 分析内容将通过 JavaScript 动态生成 -->
                    </div>
                </div>

                <!-- 个性测试结果 -->
                <div class="personality-section hidden" id="personalitySection">
                    <h3 class="personality-title">你的性格类型</h3>
                    <div class="personality-result" id="personalityResult">
                        <!-- 个性测试结果将通过 JavaScript 动态生成 -->
                    </div>
                </div>

                <!-- 测验统计 -->
                <div class="stats-section">
                    <h3 class="stats-title">测验统计</h3>
                    <div class="stats-grid">
                        <div class="stat-item">
                            <div class="stat-icon">⏱️</div>
                            <div class="stat-info">
                                <div class="stat-label">用时</div>
                                <div class="stat-value" id="timeSpent">--</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">📊</div>
                            <div class="stat-info">
                                <div class="stat-label">正确率</div>
                                <div class="stat-value" id="accuracy">--</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">🎯</div>
                            <div class="stat-info">
                                <div class="stat-label">分类</div>
                                <div class="stat-value" id="category">--</div>
                            </div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-icon">📅</div>
                            <div class="stat-info">
                                <div class="stat-label">完成时间</div>
                                <div class="stat-value" id="completedTime">--</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 题目回顾 -->
                <div class="review-section">
                    <h3 class="review-title">题目回顾</h3>
                    <div class="review-content" id="reviewContent">
                        <!-- 题目回顾将通过 JavaScript 动态生成 -->
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="result-actions">
                    <button class="btn btn-primary" onclick="retakeQuiz()">重新测验</button>
                    <button class="btn btn-secondary" onclick="shareResult()">分享结果</button>
                    <a href="index.html" class="btn btn-outline">返回首页</a>
                </div>
            </div>
        </div>
    </main>

    <!-- 分享模态框 -->
    <div class="modal-overlay" id="shareModal">
        <div class="modal">
            <div class="modal-header">
                <h3>分享你的结果</h3>
                <button class="modal-close" onclick="closeShareModal()">×</button>
            </div>
            <div class="modal-body">
                <div class="share-preview" id="sharePreview">
                    <!-- 分享预览内容 -->
                </div>
                <div class="share-options">
                    <button class="share-btn" onclick="shareToSocial('facebook')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                        </svg>
                        Facebook
                    </button>
                    <button class="share-btn" onclick="shareToSocial('twitter')">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                        </svg>
                        Twitter
                    </button>
                    <button class="share-btn" onclick="copyResultLink()">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                            <path d="14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                        </svg>
                        复制链接
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="result.js"></script>
</body>
</html>

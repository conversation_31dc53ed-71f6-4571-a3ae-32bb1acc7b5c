/* 文章页面专用样式 */

/* 文章主体 */
.article-main {
    padding: var(--spacing-8) 0;
    background-color: var(--gray-50);
    min-height: calc(100vh - 160px);
}

.article-layout {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .article-layout {
        grid-template-columns: 2fr 1fr;
    }
}

/* 文章内容 */
.article-content {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-lg);
    overflow: hidden;
}

/* 文章头部 */
.article-header {
    padding: var(--spacing-8);
    border-bottom: 1px solid var(--gray-200);
}

.article-breadcrumb {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-4);
    font-size: var(--font-size-sm);
    color: var(--gray-500);
}

.article-breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.article-breadcrumb a:hover {
    color: var(--primary-hover);
}

.article-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--gray-900);
    line-height: 1.2;
    margin-bottom: var(--spacing-6);
}

.article-meta {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: var(--spacing-4);
    margin-bottom: var(--spacing-6);
}

.meta-item {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-1);
}

.meta-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-transform: uppercase;
    font-weight: 500;
}

.meta-value {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    font-weight: 500;
}

.article-image {
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    margin-top: var(--spacing-6);
}

.article-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
}

/* 文章正文 */
.article-body {
    padding: var(--spacing-8);
    line-height: 1.8;
    color: var(--gray-700);
}

.article-intro {
    font-size: var(--font-size-lg);
    color: var(--gray-600);
    font-style: italic;
    margin-bottom: var(--spacing-8);
    padding: var(--spacing-6);
    background-color: var(--gray-50);
    border-left: 4px solid var(--primary-color);
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.article-body h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin: var(--spacing-8) 0 var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--primary-color);
}

.article-body h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-800);
    margin: var(--spacing-6) 0 var(--spacing-3);
}

.article-body h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-700);
    margin: var(--spacing-4) 0 var(--spacing-2);
}

.article-body p {
    margin-bottom: var(--spacing-4);
}

.article-body ul,
.article-body ol {
    margin: var(--spacing-4) 0;
    padding-left: var(--spacing-6);
}

.article-body li {
    margin-bottom: var(--spacing-2);
}

.article-body strong {
    color: var(--gray-900);
    font-weight: 600;
}

/* 特殊内容框 */
.highlight-box,
.tip-box,
.info-box,
.warning-box {
    margin: var(--spacing-6) 0;
    padding: var(--spacing-4);
    border-radius: var(--border-radius-lg);
    border-left: 4px solid;
}

.highlight-box {
    background-color: var(--primary-light);
    border-left-color: var(--primary-color);
}

.tip-box {
    background-color: #f0fdf4;
    border-left-color: var(--success-color);
}

.info-box {
    background-color: #eff6ff;
    border-left-color: var(--info-color);
}

.warning-box {
    background-color: #fef3cd;
    border-left-color: var(--warning-color);
}

.highlight-box h4,
.tip-box h4,
.info-box h4,
.warning-box h4 {
    margin-top: 0;
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-base);
    font-weight: 600;
}

/* 文章标签 */
.article-tags {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-2);
    margin-top: var(--spacing-8);
    padding-top: var(--spacing-6);
    border-top: 1px solid var(--gray-200);
}

.article-tags .tag {
    padding: var(--spacing-1) var(--spacing-3);
    background-color: var(--gray-100);
    color: var(--gray-700);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-sm);
    text-decoration: none;
    transition: var(--transition-fast);
}

.article-tags .tag:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* 文章底部 */
.article-footer {
    padding: var(--spacing-8);
    border-top: 1px solid var(--gray-200);
    background-color: var(--gray-50);
}

.article-share {
    margin-bottom: var(--spacing-8);
}

.article-share h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
}

.share-buttons {
    display: flex;
    gap: var(--spacing-3);
    flex-wrap: wrap;
}

.share-btn {
    padding: var(--spacing-2) var(--spacing-4);
    background-color: var(--white);
    color: var(--gray-700);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.share-btn:hover {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

/* 文章导航 */
.article-navigation {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

@media (min-width: 640px) {
    .article-navigation {
        grid-template-columns: 1fr 1fr;
    }
}

.nav-link {
    display: flex;
    flex-direction: column;
    padding: var(--spacing-4);
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius-lg);
    text-decoration: none;
    transition: var(--transition-fast);
}

.nav-link:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow);
}

.nav-label {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
    text-transform: uppercase;
    font-weight: 500;
    margin-bottom: var(--spacing-1);
}

.nav-title {
    font-size: var(--font-size-sm);
    color: var(--gray-700);
    font-weight: 500;
}

.next-article {
    text-align: right;
}

/* 侧边栏 */
.article-sidebar {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.sidebar-widget {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
}

.widget-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-4);
    padding-bottom: var(--spacing-2);
    border-bottom: 2px solid var(--primary-color);
}

/* 文章目录 */
.article-toc {
    position: sticky;
    top: 100px;
}

.article-toc ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.article-toc li {
    margin-bottom: var(--spacing-1);
}

.article-toc a {
    display: block;
    padding: var(--spacing-1) var(--spacing-2);
    color: var(--gray-600);
    text-decoration: none;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
    line-height: 1.4;
}

.article-toc a:hover,
.article-toc a.active {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.article-toc .toc-h3 {
    padding-left: var(--spacing-4);
}

.article-toc .toc-h4 {
    padding-left: var(--spacing-6);
}

/* 作者卡片 */
.author-card {
    display: flex;
    gap: var(--spacing-4);
}

.author-avatar {
    flex-shrink: 0;
}

.author-avatar img {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
}

.author-info {
    flex: 1;
}

.author-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-1);
}

.author-title {
    font-size: var(--font-size-sm);
    color: var(--primary-color);
    margin-bottom: var(--spacing-2);
}

.author-bio {
    font-size: var(--font-size-sm);
    color: var(--gray-600);
    line-height: 1.5;
}

/* 相关文章 */
.related-articles {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-4);
}

.related-article {
    display: flex;
    gap: var(--spacing-3);
}

.related-image {
    flex-shrink: 0;
}

.related-image img {
    width: 80px;
    height: 60px;
    object-fit: cover;
    border-radius: var(--border-radius);
}

.related-content {
    flex: 1;
}

.related-title {
    font-size: var(--font-size-sm);
    font-weight: 500;
    margin-bottom: var(--spacing-1);
    line-height: 1.4;
}

.related-title a {
    color: var(--gray-800);
    text-decoration: none;
    transition: var(--transition-fast);
}

.related-title a:hover {
    color: var(--primary-color);
}

.related-date {
    font-size: var(--font-size-xs);
    color: var(--gray-500);
}

/* 响应式设计 */
@media (max-width: 1023px) {
    .article-sidebar {
        order: -1;
    }
}

@media (max-width: 768px) {
    .article-header,
    .article-body,
    .article-footer {
        padding: var(--spacing-6);
    }
    
    .article-title {
        font-size: var(--font-size-2xl);
    }
    
    .article-meta {
        grid-template-columns: 1fr;
        gap: var(--spacing-2);
    }
    
    .article-image img {
        height: 250px;
    }
    
    .article-body h2 {
        font-size: var(--font-size-xl);
    }
    
    .article-body h3 {
        font-size: var(--font-size-lg);
    }
    
    .author-card {
        flex-direction: column;
        text-align: center;
    }
    
    .author-avatar {
        align-self: center;
    }
}

@media (max-width: 480px) {
    .article-header,
    .article-body,
    .article-footer {
        padding: var(--spacing-4);
    }
    
    .article-title {
        font-size: var(--font-size-xl);
    }
    
    .article-image img {
        height: 200px;
    }
    
    .share-buttons {
        flex-direction: column;
    }
    
    .share-btn {
        text-align: center;
    }
    
    .article-navigation {
        grid-template-columns: 1fr;
    }
}

/* 阅读进度条 */
.reading-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--gray-200);
    z-index: 1000;
}

.reading-progress-bar {
    height: 100%;
    background-color: var(--primary-color);
    width: 0%;
    transition: width 0.1s ease;
}

/* 代码块样式 */
.article-body pre {
    background-color: var(--gray-900);
    color: var(--gray-100);
    padding: var(--spacing-4);
    border-radius: var(--border-radius-lg);
    overflow-x: auto;
    margin: var(--spacing-4) 0;
}

.article-body code {
    background-color: var(--gray-100);
    color: var(--gray-800);
    padding: 2px 4px;
    border-radius: var(--border-radius-sm);
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.article-body pre code {
    background: none;
    color: inherit;
    padding: 0;
}

/* 引用样式 */
.article-body blockquote {
    margin: var(--spacing-6) 0;
    padding: var(--spacing-4) var(--spacing-6);
    border-left: 4px solid var(--primary-color);
    background-color: var(--gray-50);
    font-style: italic;
    color: var(--gray-600);
}

/* 表格样式 */
.article-body table {
    width: 100%;
    border-collapse: collapse;
    margin: var(--spacing-4) 0;
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
}

.article-body th,
.article-body td {
    padding: var(--spacing-3);
    text-align: left;
    border-bottom: 1px solid var(--gray-200);
}

.article-body th {
    background-color: var(--gray-50);
    font-weight: 600;
    color: var(--gray-900);
}

.article-body tr:last-child td {
    border-bottom: none;
}

// 博客页面专用脚本

// DOM 元素
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const navMenu = document.getElementById('navMenu');
const blogSearch = document.getElementById('blogSearch');
const blogSearchBtn = document.getElementById('blogSearchBtn');
const newsletterForm = document.querySelector('.newsletter-form');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeBlogPage();
});

// 初始化博客页面
function initializeBlogPage() {
    // 移动端菜单切换
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 初始化搜索功能
    initializeSearch();
    
    // 初始化分页
    initializePagination();
    
    // 初始化订阅表单
    initializeNewsletter();
    
    // 添加滚动动画
    addScrollAnimations();
    
    // 添加返回顶部按钮
    addBackToTopButton();
    
    // 添加文章卡片交互
    addArticleCardEffects();
    
    // 添加分类过滤
    addCategoryFiltering();
    
    // 添加标签过滤
    addTagFiltering();
}

// 切换移动端菜单
function toggleMobileMenu() {
    navMenu.classList.toggle('active');
    
    // 切换汉堡菜单图标动画
    const spans = mobileMenuBtn.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) span.style.opacity = '0';
            if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            span.style.transform = '';
            span.style.opacity = '';
        }
    });
}

// 初始化搜索功能
function initializeSearch() {
    if (!blogSearch || !blogSearchBtn) return;
    
    // 搜索按钮点击事件
    blogSearchBtn.addEventListener('click', performSearch);
    
    // 回车键搜索
    blogSearch.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 实时搜索（防抖）
    blogSearch.addEventListener('input', debounce(function() {
        const query = this.value.trim();
        if (query.length > 2) {
            performSearch();
        } else if (query.length === 0) {
            clearSearchResults();
        }
    }, 500));
}

// 执行搜索
function performSearch() {
    const query = blogSearch.value.trim().toLowerCase();
    if (!query) return;
    
    // 显示加载状态
    showSearchLoading();
    
    // 模拟搜索延迟
    setTimeout(() => {
        const articles = document.querySelectorAll('.article-card, .featured-article');
        let hasResults = false;
        
        articles.forEach(article => {
            const title = article.querySelector('.article-title').textContent.toLowerCase();
            const excerpt = article.querySelector('.article-excerpt').textContent.toLowerCase();
            const category = article.querySelector('.article-category').textContent.toLowerCase();
            
            if (title.includes(query) || excerpt.includes(query) || category.includes(query)) {
                article.style.display = '';
                highlightSearchTerms(article, query);
                hasResults = true;
            } else {
                article.style.display = 'none';
            }
        });
        
        hideSearchLoading();
        
        if (!hasResults) {
            showNoResults(query);
        } else {
            hideNoResults();
        }
    }, 500);
}

// 清除搜索结果
function clearSearchResults() {
    const articles = document.querySelectorAll('.article-card, .featured-article');
    articles.forEach(article => {
        article.style.display = '';
        clearHighlights(article);
    });
    hideNoResults();
}

// 高亮搜索词
function highlightSearchTerms(article, query) {
    const textElements = article.querySelectorAll('.article-title, .article-excerpt');
    
    textElements.forEach(element => {
        const text = element.textContent;
        const regex = new RegExp(`(${escapeRegex(query)})`, 'gi');
        const highlightedText = text.replace(regex, '<span class="search-highlight">$1</span>');
        
        if (highlightedText !== text) {
            element.innerHTML = highlightedText;
        }
    });
}

// 清除高亮
function clearHighlights(article) {
    const highlights = article.querySelectorAll('.search-highlight');
    highlights.forEach(highlight => {
        const parent = highlight.parentNode;
        parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
        parent.normalize();
    });
}

// 转义正则表达式特殊字符
function escapeRegex(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 显示搜索加载状态
function showSearchLoading() {
    const articlesGrid = document.querySelector('.articles-grid');
    if (!document.querySelector('.loading')) {
        const loadingDiv = document.createElement('div');
        loadingDiv.className = 'loading';
        loadingDiv.innerHTML = '<div class="loading-spinner"></div>';
        articlesGrid.appendChild(loadingDiv);
    }
}

// 隐藏搜索加载状态
function hideSearchLoading() {
    const loading = document.querySelector('.loading');
    if (loading) {
        loading.remove();
    }
}

// 显示无结果状态
function showNoResults(query) {
    const articlesGrid = document.querySelector('.articles-grid');
    if (!document.querySelector('.no-results')) {
        const noResultsDiv = document.createElement('div');
        noResultsDiv.className = 'no-results';
        noResultsDiv.innerHTML = `
            <h3>未找到相关文章</h3>
            <p>没有找到包含"${query}"的文章，请尝试其他关键词。</p>
            <button class="btn btn-primary" onclick="clearSearchResults(); blogSearch.value = '';">查看所有文章</button>
        `;
        articlesGrid.appendChild(noResultsDiv);
    }
}

// 隐藏无结果状态
function hideNoResults() {
    const noResults = document.querySelector('.no-results');
    if (noResults) {
        noResults.remove();
    }
}

// 初始化分页
function initializePagination() {
    const paginationBtns = document.querySelectorAll('.pagination-btn');
    
    paginationBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            if (this.classList.contains('pagination-next')) {
                // 下一页逻辑
                const currentActive = document.querySelector('.pagination-btn.active');
                const currentPage = parseInt(currentActive.textContent);
                const nextPage = currentPage + 1;
                
                if (nextPage <= 10) { // 假设总共10页
                    currentActive.classList.remove('active');
                    const nextBtn = Array.from(paginationBtns).find(b => b.textContent == nextPage);
                    if (nextBtn) {
                        nextBtn.classList.add('active');
                    }
                    loadPage(nextPage);
                }
            } else if (!isNaN(parseInt(this.textContent))) {
                // 数字页码
                paginationBtns.forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                loadPage(parseInt(this.textContent));
            }
        });
    });
}

// 加载页面内容
function loadPage(pageNumber) {
    console.log(`加载第 ${pageNumber} 页`);
    
    // 滚动到顶部
    window.scrollTo({
        top: 0,
        behavior: 'smooth'
    });
    
    // 这里可以添加实际的页面加载逻辑
    // 比如从API获取数据并更新文章列表
}

// 初始化订阅表单
function initializeNewsletter() {
    if (!newsletterForm) return;
    
    newsletterForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const emailInput = this.querySelector('.newsletter-input');
        const email = emailInput.value.trim();
        
        if (!isValidEmail(email)) {
            showToast('请输入有效的邮箱地址', 'error');
            return;
        }
        
        // 模拟订阅请求
        const submitBtn = this.querySelector('.newsletter-btn');
        const originalText = submitBtn.textContent;
        
        submitBtn.textContent = '订阅中...';
        submitBtn.disabled = true;
        
        setTimeout(() => {
            showToast('订阅成功！感谢您的关注。', 'success');
            emailInput.value = '';
            submitBtn.textContent = originalText;
            submitBtn.disabled = false;
        }, 1500);
    });
}

// 添加滚动动画
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll(
        '.article-card, .featured-article, .sidebar-widget'
    );
    
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// 添加返回顶部按钮
function addBackToTopButton() {
    const backToTopButton = document.createElement('button');
    backToTopButton.className = 'back-to-top';
    backToTopButton.innerHTML = `
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m18 15-6-6-6 6"/>
        </svg>
    `;
    backToTopButton.setAttribute('aria-label', '返回顶部');
    
    document.body.appendChild(backToTopButton);
    
    // 滚动监听
    window.addEventListener('scroll', throttle(function() {
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('visible');
        } else {
            backToTopButton.classList.remove('visible');
        }
    }, 100));
    
    // 点击返回顶部
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 添加文章卡片交互效果
function addArticleCardEffects() {
    const articleCards = document.querySelectorAll('.article-card, .featured-article');
    
    articleCards.forEach(card => {
        // 鼠标悬停效果
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
        });
        
        // 点击效果
        card.addEventListener('click', function(e) {
            if (e.target.tagName !== 'A') {
                const link = this.querySelector('.article-title a');
                if (link) {
                    window.location.href = link.href;
                }
            }
        });
    });
}

// 添加分类过滤
function addCategoryFiltering() {
    const categoryLinks = document.querySelectorAll('.category-link');
    
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const categoryName = this.textContent.split('(')[0].trim();
            filterByCategory(categoryName);
            
            // 更新活动状态
            categoryLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// 按分类过滤文章
function filterByCategory(categoryName) {
    const articles = document.querySelectorAll('.article-card, .featured-article');
    
    articles.forEach(article => {
        const articleCategory = article.querySelector('.article-category').textContent;
        
        if (categoryName === '全部' || articleCategory === categoryName) {
            article.style.display = '';
        } else {
            article.style.display = 'none';
        }
    });
}

// 添加标签过滤
function addTagFiltering() {
    const tags = document.querySelectorAll('.tag');
    
    tags.forEach(tag => {
        tag.addEventListener('click', function(e) {
            e.preventDefault();
            
            const tagName = this.textContent;
            filterByTag(tagName);
            
            // 更新活动状态
            tags.forEach(t => t.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// 按标签过滤文章
function filterByTag(tagName) {
    // 这里可以实现按标签过滤的逻辑
    // 由于示例中没有为文章添加标签数据，这里只是演示
    console.log(`按标签过滤: ${tagName}`);
    showToast(`正在显示标签"${tagName}"的相关文章`, 'info');
}

// 验证邮箱格式
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 显示提示消息
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast toast-${type}`;
    toast.textContent = message;
    
    const colors = {
        success: 'var(--success-color)',
        error: 'var(--danger-color)',
        info: 'var(--primary-color)',
        warning: 'var(--warning-color)'
    };
    
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: ${colors[type] || colors.info};
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
        max-width: 300px;
        word-wrap: break-word;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        }, 300);
    }, 3000);
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 添加键盘快捷键
document.addEventListener('keydown', function(e) {
    // Ctrl+K 或 Cmd+K 聚焦搜索框
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        if (blogSearch) {
            blogSearch.focus();
        }
    }
    
    // ESC 清除搜索
    if (e.key === 'Escape' && blogSearch) {
        blogSearch.value = '';
        clearSearchResults();
        blogSearch.blur();
    }
});

// 页面可见性变化时的处理
document.addEventListener('visibilitychange', function() {
    if (document.visibilityState === 'visible') {
        // 页面重新可见时，可以刷新数据或重新启动动画
        console.log('页面重新可见');
    }
});

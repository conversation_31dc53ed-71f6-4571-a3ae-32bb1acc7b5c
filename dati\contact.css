/* 联系页面专用样式 */

/* 联系页面主体 */
.contact-main {
    padding-top: 0;
}

/* 英雄区域 */
.contact-hero {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: var(--white);
    padding: var(--spacing-16) 0;
    text-align: center;
}

.hero-title {
    font-size: var(--font-size-4xl);
    font-weight: 700;
    margin-bottom: var(--spacing-4);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    opacity: 0.9;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
}

/* 联系信息区域 */
.contact-info-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.contact-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-8);
}

@media (min-width: 768px) {
    .contact-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

.contact-card {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    text-align: center;
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
}

.contact-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.contact-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-4);
}

.contact-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.contact-description {
    color: var(--gray-600);
    margin-bottom: var(--spacing-4);
    line-height: 1.5;
}

.contact-details {
    text-align: left;
}

.contact-item {
    margin-bottom: var(--spacing-3);
    line-height: 1.5;
}

.contact-item:last-child {
    margin-bottom: 0;
}

.contact-item strong {
    color: var(--gray-900);
    font-weight: 600;
}

.contact-item a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

.contact-item a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* 联系表单区域 */
.contact-form-section {
    padding: var(--spacing-20) 0;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-8);
    box-shadow: var(--shadow-lg);
}

.form-header {
    text-align: center;
    margin-bottom: var(--spacing-8);
}

.form-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.form-subtitle {
    color: var(--gray-600);
    line-height: 1.5;
}

/* 表单样式 */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-6);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-4);
}

@media (min-width: 640px) {
    .form-row {
        grid-template-columns: 1fr 1fr;
    }
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.form-input,
.form-select,
.form-textarea {
    padding: var(--spacing-3);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    font-size: var(--font-size-base);
    transition: var(--transition-fast);
    background-color: var(--white);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: inherit;
}

.form-select {
    cursor: pointer;
}

/* 复选框样式 */
.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-2);
    cursor: pointer;
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--gray-300);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition-fast);
    flex-shrink: 0;
    margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: var(--white);
    font-size: 12px;
    font-weight: bold;
}

.checkbox-label a {
    color: var(--primary-color);
    text-decoration: none;
}

.checkbox-label a:hover {
    text-decoration: underline;
}

/* 表单操作按钮 */
.form-actions {
    display: flex;
    gap: var(--spacing-4);
    justify-content: center;
    margin-top: var(--spacing-4);
}

@media (max-width: 640px) {
    .form-actions {
        flex-direction: column;
    }
}

.btn {
    position: relative;
    overflow: hidden;
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 常见问题区域 */
.faq-section {
    padding: var(--spacing-20) 0;
    background-color: var(--gray-50);
}

.faq-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
}

@media (min-width: 768px) {
    .faq-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

.faq-item {
    background-color: var(--white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-6);
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
}

.faq-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.faq-question {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-3);
}

.faq-answer {
    color: var(--gray-700);
    line-height: 1.6;
}

/* 社交媒体区域 */
.social-section {
    padding: var(--spacing-20) 0;
}

.section-subtitle {
    text-align: center;
    color: var(--gray-600);
    margin-bottom: var(--spacing-12);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.social-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--spacing-6);
}

@media (min-width: 640px) {
    .social-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .social-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

.social-card {
    background-color: var(--white);
    border-radius: var(--border-radius-xl);
    padding: var(--spacing-6);
    text-align: center;
    text-decoration: none;
    color: inherit;
    box-shadow: var(--shadow);
    transition: var(--transition-normal);
}

.social-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
}

.social-icon {
    font-size: 2.5rem;
    margin-bottom: var(--spacing-3);
}

.social-title {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-2);
}

.social-description {
    color: var(--gray-600);
    font-size: var(--font-size-sm);
}

/* 成功模态框 */
.success-icon {
    font-size: 3rem;
    text-align: center;
    margin-bottom: var(--spacing-4);
}

/* 页脚社交链接 */
.footer-social {
    display: flex;
    gap: var(--spacing-3);
}

.footer-social .social-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: var(--gray-700);
    border-radius: var(--border-radius);
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition-fast);
    font-size: 1.2rem;
}

.footer-social .social-link:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .contact-hero {
        padding: var(--spacing-12) 0;
    }
    
    .hero-title {
        font-size: var(--font-size-3xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-lg);
    }
    
    .contact-info-section,
    .contact-form-section,
    .faq-section,
    .social-section {
        padding: var(--spacing-12) 0;
    }
    
    .form-container {
        padding: var(--spacing-6);
    }
    
    .contact-card,
    .faq-item,
    .social-card {
        padding: var(--spacing-4);
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: var(--font-size-2xl);
    }
    
    .hero-subtitle {
        font-size: var(--font-size-base);
    }
    
    .form-container {
        padding: var(--spacing-4);
    }
    
    .form-title {
        font-size: var(--font-size-xl);
    }
    
    .contact-icon,
    .social-icon {
        font-size: 2rem;
    }
    
    .success-icon {
        font-size: 2rem;
    }
}

/* 动画效果 */
.contact-card,
.faq-item,
.social-card {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.contact-card.fade-in,
.faq-item.fade-in,
.social-card.fade-in {
    opacity: 1;
    transform: translateY(0);
}

/* 表单验证样式 */
.form-input.error,
.form-select.error,
.form-textarea.error {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-error {
    color: var(--danger-color);
    font-size: var(--font-size-sm);
    margin-top: var(--spacing-1);
}

.form-input.success,
.form-select.success,
.form-textarea.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 加载状态 */
.btn.loading .btn-text {
    opacity: 0;
}

.btn.loading .btn-loading {
    opacity: 1;
}

/* 工具提示 */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--gray-900);
    color: var(--white);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--border-radius);
    font-size: var(--font-size-sm);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition-fast);
    z-index: 10;
}

.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

// 测验数据库 - 包含三套示例题库
const quizData = {
    // 常识问答测验
    trivia: {
        title: "综合知识挑战",
        description: "测试你在历史、科学、地理等多个领域的知识储备",
        category: "常识问答",
        questions: [
            {
                question: "世界上最高的山峰是什么？",
                options: ["珠穆朗玛峰", "乔戈里峰", "干城章嘉峰", "洛子峰"],
                correct: 0,
                explanation: "珠穆朗玛峰海拔8848.86米，是世界最高峰。"
            },
            {
                question: "DNA的全称是什么？",
                options: ["脱氧核糖核酸", "核糖核酸", "蛋白质", "氨基酸"],
                correct: 0,
                explanation: "DNA是脱氧核糖核酸的缩写，携带遗传信息。"
            },
            {
                question: "哪个国家被称为'千湖之国'？",
                options: ["瑞典", "挪威", "芬兰", "丹麦"],
                correct: 2,
                explanation: "芬兰有超过18万个湖泊，被称为'千湖之国'。"
            },
            {
                question: "太阳系中最大的行星是？",
                options: ["土星", "木星", "海王星", "天王星"],
                correct: 1,
                explanation: "木星是太阳系中最大的行星，质量超过其他行星总和。"
            },
            {
                question: "《蒙娜丽莎》是哪位画家的作品？",
                options: ["米开朗基罗", "拉斐尔", "达芬奇", "毕加索"],
                correct: 2,
                explanation: "《蒙娜丽莎》是意大利文艺复兴时期画家达芬奇的代表作。"
            }
        ]
    },
    
    // 个性测试
    personality: {
        title: "你是哪种性格类型？",
        description: "通过心理学问题深入了解你的性格特征和行为模式",
        category: "个性测试",
        questions: [
            {
                question: "在聚会上，你通常会：",
                options: ["主动与陌生人交谈", "和熟悉的朋友待在一起", "观察其他人的互动", "尽早离开"],
                correct: -1, // 个性测试没有标准答案
                personality: ["外向型", "社交型", "观察型", "内向型"]
            },
            {
                question: "做决定时，你更倾向于：",
                options: ["凭直觉快速决定", "仔细分析所有选项", "征求他人意见", "拖延到最后一刻"],
                correct: -1,
                personality: ["直觉型", "理性型", "依赖型", "回避型"]
            },
            {
                question: "面对压力时，你会：",
                options: ["积极寻找解决方案", "寻求朋友帮助", "独自承受", "逃避问题"],
                correct: -1,
                personality: ["解决型", "社交型", "独立型", "回避型"]
            },
            {
                question: "你的理想周末是：",
                options: ["参加户外活动", "和朋友聚会", "在家读书或看电影", "学习新技能"],
                correct: -1,
                personality: ["活跃型", "社交型", "宁静型", "成长型"]
            },
            {
                question: "在团队合作中，你通常：",
                options: ["担任领导角色", "提供创意想法", "执行具体任务", "协调团队关系"],
                correct: -1,
                personality: ["领导型", "创意型", "执行型", "协调型"]
            }
        ]
    },
    
    // 美食测验
    food: {
        title: "世界美食大挑战",
        description: "探索全球各地的特色美食，测试你的美食知识",
        category: "美食测验",
        questions: [
            {
                question: "意大利面条'Spaghetti'的正确发音是？",
                options: ["斯帕盖蒂", "斯帕格提", "斯帕盖提", "斯帕格蒂"],
                correct: 0,
                explanation: "Spaghetti的正确发音是'斯帕盖蒂'。"
            },
            {
                question: "寿司最初起源于哪个国家？",
                options: ["中国", "日本", "韩国", "泰国"],
                correct: 1,
                explanation: "寿司起源于日本，是日本的传统美食。"
            },
            {
                question: "法式料理中的'Foie Gras'指的是？",
                options: ["鹅肝", "牛排", "奶酪", "红酒"],
                correct: 0,
                explanation: "Foie Gras是法语，指的是鹅肝或鸭肝。"
            },
            {
                question: "哪种香料被称为'香料之王'？",
                options: ["胡椒", "肉桂", "丁香", "豆蔻"],
                correct: 0,
                explanation: "黑胡椒因其珍贵和广泛使用被称为'香料之王'。"
            },
            {
                question: "提拉米苏起源于哪个国家？",
                options: ["法国", "意大利", "奥地利", "德国"],
                correct: 1,
                explanation: "提拉米苏是意大利的经典甜点，起源于威尼托地区。"
            }
        ]
    }
};

// 当前测验状态
let currentQuiz = null;
let currentQuestionIndex = 0;
let userAnswers = [];
let score = 0;

// DOM 元素
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const navMenu = document.getElementById('navMenu');
const searchInput = document.getElementById('searchInput');
const searchBtn = document.getElementById('searchBtn');

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// 初始化应用功能
function initializeApp() {
    // 移动端菜单切换
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 搜索功能
    if (searchBtn && searchInput) {
        searchBtn.addEventListener('click', handleSearch);
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                handleSearch();
            }
        });
    }
    
    // 分类卡片点击事件
    const categoryCards = document.querySelectorAll('.category-card');
    categoryCards.forEach(card => {
        card.addEventListener('click', function() {
            const category = this.dataset.category;
            scrollToQuizzes();
            highlightCategory(category);
        });
    });
    
    // 添加滚动动画
    addScrollAnimations();
    
    // 平滑滚动到锚点
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 切换移动端菜单
function toggleMobileMenu() {
    navMenu.classList.toggle('active');
    
    // 切换汉堡菜单图标动画
    const spans = mobileMenuBtn.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) span.style.opacity = '0';
            if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            span.style.transform = '';
            span.style.opacity = '';
        }
    });
}

// 处理搜索
function handleSearch() {
    const query = searchInput.value.trim();
    if (query) {
        // 这里可以实现搜索逻辑
        console.log('搜索:', query);
        // 简单的搜索实现：滚动到相关分类
        const categories = ['trivia', 'personality', 'food', 'lifestyle'];
        const matchedCategory = categories.find(cat => 
            cat.includes(query.toLowerCase()) || 
            query.toLowerCase().includes(cat)
        );
        
        if (matchedCategory) {
            scrollToQuizzes();
            highlightCategory(matchedCategory);
        }
    }
}

// 滚动到测验区域
function scrollToQuizzes() {
    const quizzesSection = document.getElementById('quizzes');
    if (quizzesSection) {
        quizzesSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 高亮显示分类
function highlightCategory(category) {
    // 移除所有高亮
    document.querySelectorAll('.category-card').forEach(card => {
        card.classList.remove('highlighted');
    });
    
    // 添加高亮到指定分类
    const targetCard = document.querySelector(`[data-category="${category}"]`);
    if (targetCard) {
        targetCard.classList.add('highlighted');
        setTimeout(() => {
            targetCard.classList.remove('highlighted');
        }, 2000);
    }
}

// 开始测验
function startQuiz(quizType) {
    if (!quizData[quizType]) {
        console.error('测验类型不存在:', quizType);
        return;
    }
    
    // 保存测验类型到 localStorage
    localStorage.setItem('currentQuizType', quizType);
    
    // 跳转到测验页面
    window.location.href = 'quiz.html';
}

// 添加滚动动画
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll(
        '.category-card, .quiz-card, .stat-item, .hero-content, .hero-image'
    );
    
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// 工具函数：格式化时间
function formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
}

// 工具函数：随机打乱数组
function shuffleArray(array) {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 添加高亮样式到 CSS（通过 JavaScript 动态添加）
const style = document.createElement('style');
style.textContent = `
    .category-card.highlighted {
        transform: translateY(-8px) scale(1.02);
        border-color: var(--primary-color);
        box-shadow: 0 20px 25px -5px rgba(99, 102, 241, 0.2), 0 10px 10px -5px rgba(99, 102, 241, 0.1);
    }
    
    .quiz-card {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .quiz-card.fade-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .category-card {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .category-card.fade-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .stat-item {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.6s ease-out;
    }
    
    .stat-item.fade-in {
        opacity: 1;
        transform: translateY(0);
    }
    
    .hero-content {
        opacity: 0;
        transform: translateX(-30px);
        transition: all 0.8s ease-out;
    }
    
    .hero-content.fade-in {
        opacity: 1;
        transform: translateX(0);
    }
    
    .hero-image {
        opacity: 0;
        transform: translateX(30px);
        transition: all 0.8s ease-out;
    }
    
    .hero-image.fade-in {
        opacity: 1;
        transform: translateX(0);
    }
`;
document.head.appendChild(style);

// 新首页功能

// 初始化分类标签
function initializeCategoryTabs() {
    const categoryTabs = document.querySelectorAll('.category-tab');

    categoryTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // 移除所有活动状态
            categoryTabs.forEach(t => t.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');

            // 切换内容
            const category = this.dataset.category;
            switchCategoryContent(category);
        });
    });
}

// 切换分类内容
function switchCategoryContent(category) {
    const categoryCards = document.querySelector('.category-cards');
    if (!categoryCards) return;

    // 模拟不同分类的内容
    const contentData = {
        pets: [
            {
                title: "花一天时间做狗，找出你是什么品种",
                description: "你是什么样的狗狗？",
                participants: 23,
                image: "宠物",
                prompt: "cute dog breeds golden retriever labrador friendly pets"
            },
            {
                title: "你是什么狗品种和猫品种的组合？",
                description: "你是缅因猫还是哈士奇？",
                participants: 63,
                image: "猫咪",
                prompt: "cute cat breeds maine coon persian cats adorable pets"
            }
        ],
        personality: [
            {
                title: "你的性格类型是什么？",
                description: "深入了解你的内心世界",
                participants: 156,
                image: "性格",
                prompt: "personality psychology brain colorful abstract art"
            },
            {
                title: "你的情商有多高？",
                description: "测试你的情绪智力",
                participants: 89,
                image: "情商",
                prompt: "emotional intelligence psychology mental health"
            }
        ],
        food: [
            {
                title: "你最适合哪种料理风格？",
                description: "发现你的美食天赋",
                participants: 234,
                image: "料理",
                prompt: "cooking cuisine chef kitchen delicious food"
            },
            {
                title: "你的理想咖啡是什么？",
                description: "找到完美的咖啡搭配",
                participants: 178,
                image: "咖啡",
                prompt: "coffee beans espresso latte cafe culture"
            }
        ],
        knowledge: [
            {
                title: "你的科学知识有多广？",
                description: "挑战你的科学素养",
                participants: 312,
                image: "科学",
                prompt: "science laboratory microscope DNA molecules educational"
            },
            {
                title: "世界历史大挑战",
                description: "穿越时空的历史之旅",
                participants: 267,
                image: "历史",
                prompt: "ancient history books scrolls traditional culture"
            }
        ],
        entertainment: [
            {
                title: "你是哪个电影角色？",
                description: "找到你的银幕分身",
                participants: 445,
                image: "电影",
                prompt: "movie cinema film characters entertainment"
            },
            {
                title: "你的音乐品味测试",
                description: "发现你的音乐灵魂",
                participants: 389,
                image: "音乐",
                prompt: "music notes instruments concert entertainment"
            }
        ],
        travel: [
            {
                title: "你最适合去哪个国家旅行？",
                description: "发现你的理想旅行目的地",
                participants: 567,
                image: "旅行",
                prompt: "travel destinations world landmarks passport adventure"
            },
            {
                title: "你是什么类型的旅行者？",
                description: "探索你的旅行风格",
                participants: 423,
                image: "探险",
                prompt: "backpacker adventure travel explorer journey"
            }
        ],
        sports: [
            {
                title: "你最适合哪项运动？",
                description: "找到你的运动天赋",
                participants: 334,
                image: "运动",
                prompt: "sports fitness exercise athletic activities"
            },
            {
                title: "你的健康生活指数",
                description: "测试你的健康意识",
                participants: 298,
                image: "健康",
                prompt: "healthy lifestyle fitness nutrition wellness"
            }
        ],
        technology: [
            {
                title: "你是什么类型的程序员？",
                description: "发现你的编程风格",
                participants: 612,
                image: "编程",
                prompt: "programming coding computer technology developer"
            },
            {
                title: "你对AI的了解有多深？",
                description: "测试你的人工智能知识",
                participants: 445,
                image: "AI",
                prompt: "artificial intelligence robot technology future"
            }
        ],
        art: [
            {
                title: "你是哪种艺术风格？",
                description: "发现你的艺术灵魂",
                participants: 378,
                image: "艺术",
                prompt: "art painting creative artistic expression"
            },
            {
                title: "你的审美品味测试",
                description: "探索你的美学观念",
                participants: 289,
                image: "美学",
                prompt: "aesthetic beauty design visual arts culture"
            }
        ]
    };

    const content = contentData[category] || contentData.pets;

    categoryCards.innerHTML = content.map(item => `
        <div class="category-card" onclick="startQuiz('${category}')">
            <div class="card-image">
                <img src="${generateImage(item.prompt, 150, 100)}" alt="${item.title}"
                     onerror="this.src='https://via.placeholder.com/150x100/ef4444/ffffff?text=${item.image}'">
            </div>
            <div class="card-content">
                <h4 class="card-title">${item.title}</h4>
                <p class="card-description">${item.description}</p>
                <div class="card-stats">
                    <span class="participants">💬 ${item.participants}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// 初始化侧边栏功能
function initializeSidebar() {
    // 初始化排行榜标签
    const widgetTabs = document.querySelectorAll('.widget-tab');

    widgetTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const parent = this.closest('.sidebar-widget');
            const tabs = parent.querySelectorAll('.widget-tab');

            // 移除所有活动状态
            tabs.forEach(t => t.classList.remove('active'));
            // 添加当前活动状态
            this.classList.add('active');

            // 切换排行榜内容
            switchLeaderboardContent(this.textContent.trim());
        });
    });
}

// 切换排行榜内容
function switchLeaderboardContent(type) {
    const leaderboard = document.querySelector('.leaderboard');
    if (!leaderboard) return;

    // 模拟不同类型的排行榜数据
    const leaderboardData = {
        '最活跃': [
            { name: 'NotingMeNin', score: '290,320', avatar: 'N', badge: '🏆' },
            { name: 'SuperMomHill', score: '94,860', avatar: 'S', badge: '🥈' },
            { name: 'angelicamanda', score: '90,900', avatar: 'A', badge: '🥉' },
            { name: 'MaryGrayPaul', score: '84,480', avatar: 'M', badge: '' },
            { name: 'wes562', score: '73,480', avatar: 'W', badge: '' }
        ],
        '最高分': [
            { name: 'QuizMaster', score: '500,000', avatar: 'Q', badge: '🏆' },
            { name: 'BrainPower', score: '450,000', avatar: 'B', badge: '🥈' },
            { name: 'SmartCookie', score: '420,000', avatar: 'S', badge: '🥉' },
            { name: 'WiseOwl', score: '380,000', avatar: 'W', badge: '' },
            { name: 'CleverFox', score: '350,000', avatar: 'C', badge: '' }
        ]
    };

    const data = leaderboardData[type] || leaderboardData['最活跃'];

    leaderboard.innerHTML = data.map((player, index) => `
        <div class="leaderboard-item">
            <div class="rank">${index + 1}</div>
            <div class="player-info">
                <div class="player-avatar">
                    <img src="https://via.placeholder.com/40x40/6366f1/ffffff?text=${player.avatar}" alt="${player.name}">
                </div>
                <div class="player-details">
                    <div class="player-name">${player.name}</div>
                    <div class="player-score">${player.score}</div>
                </div>
            </div>
            ${player.badge ? `<div class="player-badge">${player.badge}</div>` : ''}
        </div>
    `).join('');
}

// 图片生成功能
function generateImage(prompt, width = 400, height = 300) {
    // 清理和编码提示词
    const cleanPrompt = encodeURIComponent(prompt.replace(/[^\w\s-]/g, '').trim());
    return `https://image.pollinations.ai/prompt/${cleanPrompt}?width=${width}&height=${height}&seed=${Math.floor(Math.random() * 1000000)}`;
}

// 更新图片源
function updateImages() {
    // 更新特色卡片图片
    const featuredImages = [
        { selector: '.featured-card.large img', prompt: 'science laboratory microscope DNA molecules colorful educational illustration', width: 400, height: 250 },
        { selector: '.featured-card.small img[alt*="性格"]', prompt: 'personality psychology brain colorful abstract art', width: 200, height: 120 },
        { selector: '.featured-card.small img[alt*="历史"]', prompt: 'ancient Chinese history books scrolls traditional culture', width: 200, height: 120 }
    ];

    featuredImages.forEach(img => {
        const element = document.querySelector(img.selector);
        if (element) {
            element.src = generateImage(img.prompt, img.width, img.height);
            element.onerror = function() {
                // 如果AI图片加载失败，使用备用图片
                this.src = `https://via.placeholder.com/${img.width}x${img.height}/6366f1/ffffff?text=${encodeURIComponent(img.prompt.split(' ')[0])}`;
            };
        }
    });

    // 更新分类卡片图片
    updateCategoryImages();

    // 更新最新测验图片
    updateQuizGridImages();
}

// 更新分类卡片图片
function updateCategoryImages() {
    const categoryImages = [
        { selector: '.category-card img[alt*="宠物"]', prompt: 'cute dog breeds golden retriever labrador friendly pets', width: 150, height: 100 },
        { selector: '.category-card img[alt*="猫咪"]', prompt: 'cute cat breeds maine coon persian cats adorable pets', width: 150, height: 100 }
    ];

    categoryImages.forEach(img => {
        const element = document.querySelector(img.selector);
        if (element) {
            element.src = generateImage(img.prompt, img.width, img.height);
            element.onerror = function() {
                this.src = `https://via.placeholder.com/${img.width}x${img.height}/ef4444/ffffff?text=${encodeURIComponent(img.prompt.split(' ')[0])}`;
            };
        }
    });
}

// 更新最新测验网格图片
function updateQuizGridImages() {
    const quizImages = [
        { selector: '.quiz-card img[alt*="美食"]', prompt: 'delicious food frozen meals colorful cuisine cooking', width: 200, height: 150 },
        { selector: '.quiz-card img[alt*="生活"]', prompt: 'modern lifestyle technology apps smartphone digital life', width: 200, height: 150 },
        { selector: '.quiz-card img[alt*="性格"]', prompt: 'personality psychology self confidence mental health', width: 200, height: 150 },
        { selector: '.quiz-card img[alt*="语言"]', prompt: 'language learning books dictionary words literature', width: 200, height: 150 },
        { selector: '.quiz-card img[alt*="地理"]', prompt: 'world geography maps countries flags global knowledge', width: 200, height: 150 }
    ];

    quizImages.forEach(img => {
        const elements = document.querySelectorAll(img.selector);
        elements.forEach(element => {
            element.src = generateImage(img.prompt, img.width, img.height);
            element.onerror = function() {
                this.src = `https://via.placeholder.com/${img.width}x${img.height}/6366f1/ffffff?text=${encodeURIComponent(img.prompt.split(' ')[0])}`;
            };
        });
    });
}

// 页面加载时初始化新功能
document.addEventListener('DOMContentLoaded', function() {
    // 新增的初始化
    initializeCategoryTabs();
    initializeSidebar();

    // 默认加载宠物分类内容
    setTimeout(() => {
        switchCategoryContent('pets');
        // 更新所有图片
        updateImages();
    }, 100);
});

// 导出函数供其他页面使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        quizData,
        startQuiz,
        formatTime,
        shuffleArray,
        debounce,
        throttle,
        initializeCategoryTabs,
        initializeSidebar
    };
}

const axios = require('axios');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 分类ID到名称的映射
const CATEGORY_NAMES = {
    9: 'General Knowledge',
    10: 'Entertainment: Books',
    11: 'Entertainment: Film',
    12: 'Entertainment: Music',
    13: 'Entertainment: Musicals & Theatres',
    14: 'Entertainment: Television',
    15: 'Entertainment: Video Games',
    16: 'Entertainment: Board Games',
    17: 'Science & Nature',
    18: 'Science: Computers',
    19: 'Science: Mathematics',
    20: 'Mythology',
    21: 'Sports',
    22: 'Geography',
    23: 'History',
    24: 'Politics',
    25: 'Art',
    26: 'Celebrities',
    27: 'Animals',
    28: 'Vehicles',
    29: 'Entertainment: Comics',
    30: 'Science: Gadgets',
    31: 'Entertainment: Japanese Anime & Manga',
    32: 'Entertainment: Cartoon & Animations',
    33: 'Entertainment: Animated & Cartoon',
    34: 'Entertainment: Video Games'
};

// 从环境变量获取配置
const config = {
    baseUrl: process.env.HTTP_URL,
    amount: process.env.AMOUNT,
    difficulty: process.env.DIFFICULTY.replace(/"/g, ''), // 移除引号
    type: process.env.TYPE.replace(/"/g, ''), // 移除引号
    categories: process.env.CATEGORY_ALL
        .replace(/[\[\]]/g, '') // 移除方括号
        .split(',')
        .map(id => parseInt(id.trim()))
};

console.log('配置信息:', config);

// 创建输出目录（如果不存在）
const outputDir = './data';
if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
}

// API响应码含义
const RESPONSE_CODES = {
    0: 'Success',
    1: 'No Results - 该分类下没有足够的题目',
    2: 'Invalid Parameter',
    3: 'Token Not Found',
    4: 'Token Empty'
};

// 请求单个分类的数据（带重试机制）
async function fetchCategoryData(categoryId, retryCount = 0) {
    const maxRetries = 5; // 增加重试次数
    const retryDelay = 5000; // 5秒重试延迟

    try {
        const url = `${config.baseUrl}?amount=${config.amount}&category=${categoryId}&difficulty=${config.difficulty}&type=${config.type}`;
        console.log(`正在请求分类 ${categoryId} (${CATEGORY_NAMES[categoryId] || 'Unknown'}): ${url}`);

        // 设置请求超时时间
        const response = await axios.get(url, {
            timeout: 15000, // 15秒超时
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
        });

        if (response.data.response_code === 0) {
            console.log(`✓ 成功获取分类 ${categoryId} 的 ${response.data.results.length} 条数据`);
            // 处理每个题目，将正确答案随机插入到选项中
            const processedResults = response.data.results.map(question => {
                const allAnswers = [...question.incorrect_answers];
                // 随机位置插入正确答案
                const randomIndex = Math.floor(Math.random() * (allAnswers.length + 1));
                allAnswers.splice(randomIndex, 0, question.correct_answer);

                return {
                    ...question,
                    all_answers: allAnswers, // 包含所有选项的数组
                    correct_answer_index: randomIndex // 正确答案在all_answers中的索引
                };
            });
            return processedResults;
        } else if (response.data.response_code === 1) {
            // 响应码1表示没有足够的题目，尝试减少数量重试
            if (retryCount < maxRetries) {
                const reducedAmount = Math.max(5, Math.floor(config.amount * 0.5)); // 减少到一半，最少5题
                const retryUrl = `${config.baseUrl}?amount=${reducedAmount}&category=${categoryId}&difficulty=${config.difficulty}&type=${config.type}`;
                console.log(`⏳ 分类 ${categoryId} 题目不足，尝试减少数量到 ${reducedAmount} 题后重试 (${retryCount + 1}/${maxRetries})`);

                await new Promise(resolve => setTimeout(resolve, retryDelay));

                try {
                    const retryResponse = await axios.get(retryUrl, {
                        timeout: 15000,
                        headers: {
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    });

                    if (retryResponse.data.response_code === 0) {
                        console.log(`✓ 成功获取分类 ${categoryId} 的 ${retryResponse.data.results.length} 条数据（减少数量后）`);
                        const processedResults = retryResponse.data.results.map(question => {
                            const allAnswers = [...question.incorrect_answers];
                            const randomIndex = Math.floor(Math.random() * (allAnswers.length + 1));
                            allAnswers.splice(randomIndex, 0, question.correct_answer);
                            return {
                                ...question,
                                all_answers: allAnswers,
                                correct_answer_index: randomIndex
                            };
                        });
                        return processedResults;
                    }
                } catch (retryError) {
                    console.log(`⚠️ 分类 ${categoryId} 减少数量后仍然失败，继续重试原数量`);
                }

                return fetchCategoryData(categoryId, retryCount + 1);
            } else {
                console.error(`✗ 分类 ${categoryId} 请求失败: ${RESPONSE_CODES[response.data.response_code] || '未知错误'}，已达到最大重试次数`);
                return [];
            }
        } else {
            // 其他API响应码错误
            if (retryCount < maxRetries) {
                const errorMsg = RESPONSE_CODES[response.data.response_code] || `未知响应码 ${response.data.response_code}`;
                console.log(`⏳ 分类 ${categoryId} API错误 (${errorMsg})，${retryDelay/1000}秒后重试 (${retryCount + 1}/${maxRetries})`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
                return fetchCategoryData(categoryId, retryCount + 1);
            } else {
                console.error(`✗ 分类 ${categoryId} 请求失败: ${RESPONSE_CODES[response.data.response_code] || '未知错误'}，已达到最大重试次数`);
                return [];
            }
        }
    } catch (error) {
        // 对所有请求错误进行重试
        if (retryCount < maxRetries) {
            let errorType = '网络错误';
            let errorMsg = error.message;

            if (error.response) {
                errorType = `HTTP ${error.response.status}`;
            } else if (error.code === 'ENOTFOUND') {
                errorType = 'DNS解析失败';
            } else if (error.code === 'ETIMEDOUT' || error.code === 'ECONNABORTED') {
                errorType = '连接超时';
            }

            console.log(`⏳ 分类 ${categoryId} 请求失败 (${errorType}: ${errorMsg})，${retryDelay/1000}秒后重试 (${retryCount + 1}/${maxRetries})`);
            await new Promise(resolve => setTimeout(resolve, retryDelay));
            return fetchCategoryData(categoryId, retryCount + 1);
        } else {
            console.error(`✗ 分类 ${categoryId} 请求出错: ${error.message}，已达到最大重试次数`);
            return [];
        }
    }
}

// 主函数
async function main() {
    console.log('开始获取题目数据...\n');
    
    const allData = {};
    
    // 循环请求所有分类
    for (const categoryId of config.categories) {
        const categoryName = CATEGORY_NAMES[categoryId] || `Category ${categoryId}`;
        const data = await fetchCategoryData(categoryId);
        
        if (data.length > 0) {
            allData[categoryName] = data;
        }
        
        // 添加延迟以避免请求过于频繁（增加到2秒）
        await new Promise(resolve => setTimeout(resolve, 2000));
    }
    
    // 保存数据到文件
    const outputPath = path.join(outputDir, 'data.json');
    try {
        fs.writeFileSync(outputPath, JSON.stringify(allData, null, 2), 'utf8');
        console.log(`\n✓ 数据已保存到: ${outputPath}`);
        console.log(`✓ 总共获取了 ${Object.keys(allData).length} 个分类的数据`);
        
        // 显示每个分类的数据数量
        Object.entries(allData).forEach(([categoryName, questions]) => {
            console.log(`  - ${categoryName}: ${questions.length} 条题目`);
        });
        
    } catch (error) {
        console.error('✗ 保存文件时出错:', error.message);
    }
}

// 运行主函数
main().catch(error => {
    console.error('程序执行出错:', error.message);
    process.exit(1);
});

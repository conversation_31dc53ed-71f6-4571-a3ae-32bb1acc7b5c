// 联系页面专用脚本

// DOM 元素
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const navMenu = document.getElementById('navMenu');
const contactForm = document.getElementById('contactForm');
const submitBtn = document.getElementById('submitBtn');
const successModal = document.getElementById('successModal');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeContactPage();
});

// 初始化联系页面
function initializeContactPage() {
    // 移动端菜单切换
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 初始化表单
    initializeForm();
    
    // 添加滚动动画
    addScrollAnimations();
    
    // 添加联系卡片交互
    addContactCardEffects();
    
    // 添加FAQ交互
    addFAQEffects();
    
    // 添加社交卡片效果
    addSocialCardEffects();
}

// 切换移动端菜单
function toggleMobileMenu() {
    navMenu.classList.toggle('active');
    
    // 切换汉堡菜单图标动画
    const spans = mobileMenuBtn.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) span.style.opacity = '0';
            if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            span.style.transform = '';
            span.style.opacity = '';
        }
    });
}

// 初始化表单
function initializeForm() {
    if (!contactForm) return;
    
    // 表单提交处理
    contactForm.addEventListener('submit', handleFormSubmit);
    
    // 实时验证
    const inputs = contactForm.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', validateField);
        input.addEventListener('input', clearFieldError);
    });
    
    // 重置表单
    const resetBtn = contactForm.querySelector('button[type="reset"]');
    if (resetBtn) {
        resetBtn.addEventListener('click', handleFormReset);
    }
}

// 处理表单提交
async function handleFormSubmit(e) {
    e.preventDefault();
    
    // 验证表单
    if (!validateForm()) {
        return;
    }
    
    // 显示加载状态
    setSubmitButtonLoading(true);
    
    try {
        // 收集表单数据
        const formData = new FormData(contactForm);
        const data = Object.fromEntries(formData.entries());
        
        // 模拟API调用
        await simulateFormSubmission(data);
        
        // 显示成功消息
        showSuccessModal();
        
        // 重置表单
        contactForm.reset();
        clearAllErrors();
        
    } catch (error) {
        console.error('表单提交失败:', error);
        showErrorMessage('发送失败，请稍后重试。');
    } finally {
        setSubmitButtonLoading(false);
    }
}

// 模拟表单提交
function simulateFormSubmission(data) {
    return new Promise((resolve, reject) => {
        // 模拟网络延迟
        setTimeout(() => {
            // 模拟成功率（90%成功）
            if (Math.random() > 0.1) {
                console.log('表单数据:', data);
                resolve();
            } else {
                reject(new Error('网络错误'));
            }
        }, 2000);
    });
}

// 验证表单
function validateForm() {
    let isValid = true;
    
    // 验证必填字段
    const requiredFields = [
        { id: 'firstName', message: '请输入姓名' },
        { id: 'email', message: '请输入邮箱地址' },
        { id: 'subject', message: '请选择主题' },
        { id: 'message', message: '请输入消息内容' },
        { id: 'privacy', message: '请同意隐私政策' }
    ];
    
    requiredFields.forEach(field => {
        const element = document.getElementById(field.id);
        if (!validateField({ target: element })) {
            isValid = false;
        }
    });
    
    return isValid;
}

// 验证单个字段
function validateField(e) {
    const field = e.target;
    const value = field.value.trim();
    const fieldId = field.id;
    
    // 清除之前的错误
    clearFieldError(e);
    
    let isValid = true;
    let errorMessage = '';
    
    // 验证规则
    switch (fieldId) {
        case 'firstName':
            if (!value) {
                errorMessage = '请输入姓名';
                isValid = false;
            } else if (value.length < 2) {
                errorMessage = '姓名至少需要2个字符';
                isValid = false;
            }
            break;
            
        case 'email':
            if (!value) {
                errorMessage = '请输入邮箱地址';
                isValid = false;
            } else if (!isValidEmail(value)) {
                errorMessage = '请输入有效的邮箱地址';
                isValid = false;
            }
            break;
            
        case 'phone':
            if (value && !isValidPhone(value)) {
                errorMessage = '请输入有效的电话号码';
                isValid = false;
            }
            break;
            
        case 'subject':
            if (!value) {
                errorMessage = '请选择主题';
                isValid = false;
            }
            break;
            
        case 'message':
            if (!value) {
                errorMessage = '请输入消息内容';
                isValid = false;
            } else if (value.length < 10) {
                errorMessage = '消息内容至少需要10个字符';
                isValid = false;
            }
            break;
            
        case 'privacy':
            if (!field.checked) {
                errorMessage = '请同意隐私政策';
                isValid = false;
            }
            break;
    }
    
    // 显示错误或成功状态
    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        showFieldSuccess(field);
    }
    
    return isValid;
}

// 显示字段错误
function showFieldError(field, message) {
    field.classList.add('error');
    field.classList.remove('success');
    
    // 移除现有错误消息
    const existingError = field.parentNode.querySelector('.form-error');
    if (existingError) {
        existingError.remove();
    }
    
    // 添加错误消息
    const errorElement = document.createElement('div');
    errorElement.className = 'form-error';
    errorElement.textContent = message;
    field.parentNode.appendChild(errorElement);
}

// 显示字段成功状态
function showFieldSuccess(field) {
    field.classList.remove('error');
    field.classList.add('success');
    
    // 移除错误消息
    const existingError = field.parentNode.querySelector('.form-error');
    if (existingError) {
        existingError.remove();
    }
}

// 清除字段错误
function clearFieldError(e) {
    const field = e.target;
    field.classList.remove('error', 'success');
    
    const existingError = field.parentNode.querySelector('.form-error');
    if (existingError) {
        existingError.remove();
    }
}

// 清除所有错误
function clearAllErrors() {
    const fields = contactForm.querySelectorAll('input, select, textarea');
    fields.forEach(field => {
        field.classList.remove('error', 'success');
    });
    
    const errors = contactForm.querySelectorAll('.form-error');
    errors.forEach(error => error.remove());
}

// 设置提交按钮加载状态
function setSubmitButtonLoading(loading) {
    const btnText = submitBtn.querySelector('.btn-text');
    const btnLoading = submitBtn.querySelector('.btn-loading');
    
    if (loading) {
        submitBtn.disabled = true;
        submitBtn.classList.add('loading');
        btnText.classList.add('hidden');
        btnLoading.classList.remove('hidden');
    } else {
        submitBtn.disabled = false;
        submitBtn.classList.remove('loading');
        btnText.classList.remove('hidden');
        btnLoading.classList.add('hidden');
    }
}

// 处理表单重置
function handleFormReset() {
    clearAllErrors();
    setTimeout(() => {
        // 重置后清除所有状态
        const fields = contactForm.querySelectorAll('input, select, textarea');
        fields.forEach(field => {
            field.classList.remove('error', 'success');
        });
    }, 100);
}

// 显示成功模态框
function showSuccessModal() {
    successModal.classList.add('active');
}

// 关闭成功模态框
function closeSuccessModal() {
    successModal.classList.remove('active');
}

// 显示错误消息
function showErrorMessage(message) {
    // 创建错误提示
    const errorDiv = document.createElement('div');
    errorDiv.className = 'error-toast';
    errorDiv.textContent = message;
    errorDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--danger-color);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(errorDiv);
    
    // 显示动画
    setTimeout(() => {
        errorDiv.style.opacity = '1';
        errorDiv.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        errorDiv.style.opacity = '0';
        errorDiv.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            document.body.removeChild(errorDiv);
        }, 300);
    }, 5000);
}

// 添加滚动动画
function addScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, observerOptions);
    
    // 观察需要动画的元素
    const animatedElements = document.querySelectorAll(
        '.contact-card, .faq-item, .social-card'
    );
    
    animatedElements.forEach(el => {
        observer.observe(el);
    });
}

// 添加联系卡片交互效果
function addContactCardEffects() {
    const contactCards = document.querySelectorAll('.contact-card');
    
    contactCards.forEach(card => {
        card.addEventListener('click', function() {
            const title = this.querySelector('.contact-title').textContent;
            
            // 根据卡片类型执行不同操作
            if (title.includes('邮箱')) {
                // 复制邮箱地址
                const email = this.querySelector('a[href^="mailto:"]');
                if (email) {
                    const emailAddress = email.textContent;
                    copyToClipboard(emailAddress);
                    showToast('邮箱地址已复制到剪贴板');
                }
            } else if (title.includes('电话')) {
                // 复制电话号码
                const phone = this.querySelector('a[href^="tel:"]');
                if (phone) {
                    const phoneNumber = phone.textContent;
                    copyToClipboard(phoneNumber);
                    showToast('电话号码已复制到剪贴板');
                }
            } else if (title.includes('地址')) {
                // 复制地址
                const address = this.querySelector('.contact-item').textContent.replace('地址：', '').trim();
                copyToClipboard(address);
                showToast('地址已复制到剪贴板');
            }
        });
    });
}

// 添加FAQ交互效果
function addFAQEffects() {
    const faqItems = document.querySelectorAll('.faq-item');
    
    faqItems.forEach(item => {
        item.addEventListener('click', function() {
            // 添加点击动画
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
}

// 添加社交卡片效果
function addSocialCardEffects() {
    const socialCards = document.querySelectorAll('.social-card');
    
    socialCards.forEach(card => {
        card.addEventListener('click', function(e) {
            e.preventDefault();
            
            const title = this.querySelector('.social-title').textContent;
            
            // 模拟跳转到社交媒体
            showToast(`即将跳转到${title}...`);
            
            // 这里可以添加实际的社交媒体链接
            setTimeout(() => {
                console.log(`跳转到${title}`);
            }, 1000);
        });
    });
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).catch(() => {
            fallbackCopyText(text);
        });
    } else {
        fallbackCopyText(text);
    }
}

// 备用复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.error('复制失败:', err);
    }
    document.body.removeChild(textArea);
}

// 显示提示消息
function showToast(message) {
    const toast = document.createElement('div');
    toast.className = 'toast';
    toast.textContent = message;
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: var(--primary-color);
        color: white;
        padding: 12px 24px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
        opacity: 0;
        transform: translateY(-20px);
        transition: all 0.3s ease;
    `;
    
    document.body.appendChild(toast);
    
    // 显示动画
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateY(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        toast.style.opacity = '0';
        toast.style.transform = 'translateY(-20px)';
        setTimeout(() => {
            document.body.removeChild(toast);
        }, 300);
    }, 3000);
}

// 验证邮箱格式
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// 验证电话号码格式
function isValidPhone(phone) {
    const phoneRegex = /^[\d\s\-\+\(\)]+$/;
    return phoneRegex.test(phone) && phone.replace(/\D/g, '').length >= 10;
}

// 点击模态框外部关闭
successModal.addEventListener('click', function(e) {
    if (e.target === successModal) {
        closeSuccessModal();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && successModal.classList.contains('active')) {
        closeSuccessModal();
    }
});

// 表单自动保存（可选功能）
function enableAutoSave() {
    const inputs = contactForm.querySelectorAll('input, select, textarea');
    
    inputs.forEach(input => {
        input.addEventListener('input', debounce(function() {
            const formData = new FormData(contactForm);
            const data = Object.fromEntries(formData.entries());
            localStorage.setItem('contactFormData', JSON.stringify(data));
        }, 1000));
    });
    
    // 页面加载时恢复数据
    const savedData = localStorage.getItem('contactFormData');
    if (savedData) {
        try {
            const data = JSON.parse(savedData);
            Object.entries(data).forEach(([key, value]) => {
                const field = contactForm.querySelector(`[name="${key}"]`);
                if (field) {
                    if (field.type === 'checkbox') {
                        field.checked = value === 'on';
                    } else {
                        field.value = value;
                    }
                }
            });
        } catch (error) {
            console.error('恢复表单数据失败:', error);
        }
    }
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 启用自动保存（可选）
// enableAutoSave();

// 表单提交成功后清除自动保存的数据
contactForm.addEventListener('submit', function() {
    localStorage.removeItem('contactFormData');
});

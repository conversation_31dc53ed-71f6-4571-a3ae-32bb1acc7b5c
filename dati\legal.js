// 法律页面通用脚本

// DOM 元素
const mobileMenuBtn = document.getElementById('mobileMenuBtn');
const navMenu = document.getElementById('navMenu');
const tocLinks = document.querySelectorAll('.legal-toc a');
const sections = document.querySelectorAll('.legal-section');

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeLegalPage();
});

// 初始化法律页面
function initializeLegalPage() {
    // 移动端菜单切换
    if (mobileMenuBtn && navMenu) {
        mobileMenuBtn.addEventListener('click', toggleMobileMenu);
    }
    
    // 初始化目录导航
    initializeTOC();
    
    // 添加返回顶部按钮
    addBackToTopButton();
    
    // 添加滚动监听
    addScrollListener();
    
    // 添加平滑滚动
    addSmoothScrolling();
    
    // 添加打印功能
    addPrintFunctionality();
    
    // 添加文本选择功能
    addTextSelectionFeatures();
}

// 切换移动端菜单
function toggleMobileMenu() {
    navMenu.classList.toggle('active');
    
    // 切换汉堡菜单图标动画
    const spans = mobileMenuBtn.querySelectorAll('span');
    spans.forEach((span, index) => {
        if (navMenu.classList.contains('active')) {
            if (index === 0) span.style.transform = 'rotate(45deg) translate(5px, 5px)';
            if (index === 1) span.style.opacity = '0';
            if (index === 2) span.style.transform = 'rotate(-45deg) translate(7px, -6px)';
        } else {
            span.style.transform = '';
            span.style.opacity = '';
        }
    });
}

// 初始化目录导航
function initializeTOC() {
    // 为目录链接添加点击事件
    tocLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetSection = document.getElementById(targetId);
            
            if (targetSection) {
                // 滚动到目标章节
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
                
                // 更新活动状态
                updateActiveTOCLink(this);
            }
        });
    });
}

// 更新活动的目录链接
function updateActiveTOCLink(activeLink) {
    tocLinks.forEach(link => {
        link.classList.remove('active');
    });
    activeLink.classList.add('active');
}

// 添加返回顶部按钮
function addBackToTopButton() {
    const backToTopButton = document.createElement('button');
    backToTopButton.className = 'back-to-top';
    backToTopButton.innerHTML = `
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="m18 15-6-6-6 6"/>
        </svg>
    `;
    backToTopButton.setAttribute('aria-label', '返回顶部');
    
    document.body.appendChild(backToTopButton);
    
    // 点击返回顶部
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    return backToTopButton;
}

// 添加滚动监听
function addScrollListener() {
    const backToTopButton = document.querySelector('.back-to-top');
    
    window.addEventListener('scroll', throttle(function() {
        // 显示/隐藏返回顶部按钮
        if (window.pageYOffset > 300) {
            backToTopButton.classList.add('visible');
        } else {
            backToTopButton.classList.remove('visible');
        }
        
        // 更新目录活动状态
        updateTOCActiveState();
    }, 100));
}

// 更新目录活动状态
function updateTOCActiveState() {
    let currentSection = null;
    
    sections.forEach(section => {
        const rect = section.getBoundingClientRect();
        if (rect.top <= 100 && rect.bottom >= 100) {
            currentSection = section;
        }
    });
    
    if (currentSection) {
        const currentId = currentSection.id;
        const currentLink = document.querySelector(`.legal-toc a[href="#${currentId}"]`);
        
        if (currentLink) {
            tocLinks.forEach(link => {
                link.classList.remove('active');
            });
            currentLink.classList.add('active');
        }
    }
}

// 添加平滑滚动
function addSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// 添加打印功能
function addPrintFunctionality() {
    // 添加打印按钮到页面头部
    const legalHeader = document.querySelector('.legal-header');
    if (legalHeader) {
        const printButton = document.createElement('button');
        printButton.className = 'btn btn-secondary print-btn';
        printButton.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="6,9 6,2 18,2 18,9"></polyline>
                <path d="M6,18H4a2,2,0,0,1-2-2V11a2,2,0,0,1,2-2H20a2,2,0,0,1,2,2v5a2,2,0,0,1-2,2H18"></path>
                <rect x="6" y="14" width="12" height="8"></rect>
            </svg>
            打印页面
        `;
        printButton.style.marginTop = 'var(--spacing-4)';
        
        printButton.addEventListener('click', function() {
            window.print();
        });
        
        legalHeader.appendChild(printButton);
    }
    
    // 打印前的处理
    window.addEventListener('beforeprint', function() {
        // 展开所有折叠的内容
        document.querySelectorAll('.collapsed').forEach(element => {
            element.classList.remove('collapsed');
        });
    });
}

// 添加文本选择功能
function addTextSelectionFeatures() {
    // 双击段落选择整段
    document.querySelectorAll('.legal-section p').forEach(paragraph => {
        paragraph.addEventListener('dblclick', function() {
            selectText(this);
        });
    });
    
    // 添加复制按钮到代码块或重要段落
    document.querySelectorAll('.highlight-box, .warning-box, .info-box').forEach(box => {
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-btn';
        copyButton.innerHTML = '复制';
        copyButton.style.cssText = `
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.8);
            border: 1px solid var(--gray-300);
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
            opacity: 0;
            transition: opacity 0.2s;
        `;
        
        box.style.position = 'relative';
        box.appendChild(copyButton);
        
        box.addEventListener('mouseenter', function() {
            copyButton.style.opacity = '1';
        });
        
        box.addEventListener('mouseleave', function() {
            copyButton.style.opacity = '0';
        });
        
        copyButton.addEventListener('click', function(e) {
            e.stopPropagation();
            const text = box.textContent.replace('复制', '').trim();
            copyToClipboard(text);
            
            // 显示复制成功提示
            copyButton.textContent = '已复制';
            setTimeout(() => {
                copyButton.textContent = '复制';
            }, 2000);
        });
    });
}

// 选择文本
function selectText(element) {
    if (window.getSelection) {
        const selection = window.getSelection();
        const range = document.createRange();
        range.selectNodeContents(element);
        selection.removeAllRanges();
        selection.addRange(range);
    }
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).catch(() => {
            fallbackCopyText(text);
        });
    } else {
        fallbackCopyText(text);
    }
}

// 备用复制方法
function fallbackCopyText(text) {
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
        document.execCommand('copy');
    } catch (err) {
        console.error('复制失败:', err);
    }
    document.body.removeChild(textArea);
}

// 添加搜索功能
function addSearchFunctionality() {
    const searchContainer = document.createElement('div');
    searchContainer.className = 'search-container';
    searchContainer.innerHTML = `
        <input type="text" placeholder="搜索内容..." class="search-input" id="legalSearch">
        <button class="search-btn" id="legalSearchBtn">搜索</button>
        <div class="search-results" id="searchResults"></div>
    `;
    
    const legalHeader = document.querySelector('.legal-header');
    if (legalHeader) {
        legalHeader.appendChild(searchContainer);
    }
    
    const searchInput = document.getElementById('legalSearch');
    const searchBtn = document.getElementById('legalSearchBtn');
    const searchResults = document.getElementById('searchResults');
    
    function performSearch() {
        const query = searchInput.value.trim().toLowerCase();
        if (!query) {
            searchResults.innerHTML = '';
            return;
        }
        
        const results = [];
        sections.forEach(section => {
            const text = section.textContent.toLowerCase();
            if (text.includes(query)) {
                const title = section.querySelector('h2').textContent;
                results.push({
                    title: title,
                    id: section.id,
                    section: section
                });
            }
        });
        
        displaySearchResults(results, query);
    }
    
    function displaySearchResults(results, query) {
        if (results.length === 0) {
            searchResults.innerHTML = '<p>未找到相关内容</p>';
        } else {
            const resultHTML = results.map(result => `
                <div class="search-result-item" onclick="scrollToSection('${result.id}')">
                    <h4>${result.title}</h4>
                </div>
            `).join('');
            searchResults.innerHTML = resultHTML;
        }
        
        searchResults.style.display = 'block';
    }
    
    searchBtn.addEventListener('click', performSearch);
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });
    
    // 点击外部关闭搜索结果
    document.addEventListener('click', function(e) {
        if (!searchContainer.contains(e.target)) {
            searchResults.style.display = 'none';
        }
    });
}

// 滚动到指定章节
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
        
        // 高亮显示章节
        section.style.backgroundColor = 'var(--primary-light)';
        setTimeout(() => {
            section.style.backgroundColor = '';
        }, 2000);
    }
}

// 添加键盘快捷键
function addKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl+F 或 Cmd+F 打开搜索
        if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
            e.preventDefault();
            const searchInput = document.getElementById('legalSearch');
            if (searchInput) {
                searchInput.focus();
            }
        }
        
        // Ctrl+P 或 Cmd+P 打印
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            // 浏览器默认处理打印
        }
        
        // Home 键返回顶部
        if (e.key === 'Home') {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
        
        // End 键滚动到底部
        if (e.key === 'End') {
            e.preventDefault();
            window.scrollTo({
                top: document.body.scrollHeight,
                behavior: 'smooth'
            });
        }
    });
}

// 工具函数：节流
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}

// 工具函数：防抖
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 延迟初始化一些功能
setTimeout(() => {
    addSearchFunctionality();
    addKeyboardShortcuts();
}, 1000);
